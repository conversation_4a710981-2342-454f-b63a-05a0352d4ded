# Beta Access Flow

We're implementing an invite-only system to control early access, manage costs, and create buzz. Users must either receive an invite code or join a waitlist to access the platform.

## 🔧 System Components

### 1. Waitlist Page

**Publicly accessible at hollywoodtable.com**

Shows a call-to-action:
> "Dinner with the legends is by invitation only. Join the waitlist or enter an invite code to access."

**Inputs:**
- Name
- Email
- (Optional) "Why do you want to join?" — for future social proof
- Auto-generate a waitlist ID for each user

### 2. Invite Code Entry Field

Below the waitlist form:
> "Already have an invite code? Enter it here."

- If valid → user skips waitlist and gets free trial (25 messages)

## 🔑 Invite Code Mechanics

**On the backend:**

Each user who's accepted gets:
- An account
- 3 unique invite codes tied to their account (can use UUIDs)

**Code rules:**
- Each invite code = 1 use only
- Once used, it's marked as "redeemed"

**When a user signs up with a valid code:**
- They skip the waitlist
- Their inviter's record is updated (referral tracking)

## 🧪 Free Trial Logic

- **Trial = 25 messages total** across any characters
- Each message sent reduces count
- When trial is over → lock conversation and show upgrade prompt

Example prompt:
> "Max would love to keep talking — unlock full access for $19.99/month."

## 📤 Referral Tracking & Bonuses (Optional for Phase 2)

Later, we can:
- Give bonus messages for referrals
- Auto-email invite codes with referral links

## 🔒 Admin Dashboard

*(Optional but helpful)*

**See:**
- Waitlist entries
- Invite codes generated and used
- Trial usage stats
- Conversion rates

## 🖼️ Simple Flow Diagram

```
User visits site
 ↓
Sees waitlist page
 ↓
User enters email
 ↓
Joins waitlist and sees "Thanks! We'll notify you."
 ↓
Admin manually invites early users
 ↓
They get 3 invite codes
 ↓
Invitee signs up with code
 ↓
Gets 25-message free trial
 ↓
User hits 25 messages
 ↓
Shown upgrade/paywall