# Implementation Summary: Subscription Upgrade/Downgrade & PAYG Credits

## Overview
This implementation adds comprehensive subscription management and pay-as-you-go (PAYG) credit functionality to the Hollywood Noir Dialogue application. Users can now upgrade/downgrade their subscription plans and purchase additional credits when they reach their usage limits.

## New Edge Functions

### 1. upgrade-subscription
**Location:** `supabase/functions/upgrade-subscription/index.ts`

**Purpose:** Handles subscription upgrades from free tier to paid tiers or between paid tiers.

**Features:**
- Validates target tier and billing cycle
- Creates or updates Stripe customer
- Modifies existing Stripe subscriptions with proration
- Creates new subscriptions for first-time subscribers
- Updates database with new tier limits
- Returns payment intent for new subscriptions requiring payment

**API:**
```typescript
POST /functions/v1/upgrade-subscription
{
  "target_tier": "Reserved Seat" | "VIP Table",
  "billing_cycle": "monthly" | "annual"
}
```

### 2. downgrade-subscription
**Location:** `supabase/functions/downgrade-subscription/index.ts`

**Purpose:** Handles subscription downgrades, primarily to free tier.

**Features:**
- Validates downgrade request (prevents upgrading via downgrade endpoint)
- Cancels Stripe subscription at period end
- Updates database status to 'canceled'
- Preserves access until end of billing period

**API:**
```typescript
POST /functions/v1/downgrade-subscription
{
  "target_tier": "Sample the Table"
}
```

## Enhanced Components

### 1. Pricing Page (`src/pages/Pricing.tsx`)
**New Features:**
- Real upgrade/downgrade functionality
- Dynamic button states based on current subscription
- Integration with new edge functions
- Handles Stripe payment flows

**Key Functions:**
- `handleUpgrade(targetTier)` - Calls upgrade-subscription function
- `handleDowngrade(targetTier)` - Calls downgrade-subscription function
- `getButtonConfig(tierName)` - Determines appropriate button text and action

### 2. Chat Page (`src/pages/Chat.tsx`)
**New Features:**
- Auto-opens PAYG modal when usage limits exceeded
- Enhanced usage limit error display
- Buy Credits and Upgrade Plan buttons in error alert
- Clears usage errors after successful credit purchase

**Key Functions:**
- `handlePaygPurchaseComplete()` - Clears usage errors after purchase
- Enhanced error handling for 429 status codes

### 3. PaygCreditsModal (`src/components/PaygCreditsModal.tsx`)
**Features:**
- Three credit packages (25, 60, 150 credits)
- Real-time pricing calculation
- Integration with purchase-payg-credits function
- Simulated payment flow (ready for Stripe Elements integration)

### 4. UsageDisplay (`src/components/UsageDisplay.tsx`)
**Features:**
- Real-time usage tracking
- Visual progress bars with color coding
- PAYG credits display
- Purchase credits button for users approaching limits

## Database Integration

### Existing Functions Used:
- `update_subscription_tier_limits()` - Updates tier limits in database
- `add_payg_credits()` - Adds purchased credits to user account
- `can_user_perform_action()` - Checks usage limits

### Tables Updated:
- `user_subscriptions` - Subscription status, tier, Stripe IDs
- `payg_purchase_history` - Credit purchase tracking

## Usage Flow

### Upgrade Flow:
1. User clicks "Upgrade" on pricing page
2. `handleUpgrade()` calls upgrade-subscription function
3. Function creates/updates Stripe subscription
4. Database updated with new tier limits
5. User gains immediate access to higher limits

### Downgrade Flow:
1. User clicks "Downgrade" on pricing page
2. `handleDowngrade()` calls downgrade-subscription function
3. Stripe subscription canceled at period end
4. Database marked as 'canceled'
5. User retains access until billing period ends

### PAYG Credit Purchase Flow:
1. User hits usage limit in chat
2. Usage error displayed with "Buy Credits" button
3. PAYG modal opens automatically
4. User selects credit package and purchases
5. Credits added to account via webhook
6. User can continue chatting immediately

## Testing Plan

### Manual Testing:

#### 1. Upgrade Testing:
- [ ] Free tier user upgrades to Reserved Seat
- [ ] Reserved Seat user upgrades to VIP Table
- [ ] Verify Stripe subscription creation/modification
- [ ] Verify database tier limits updated
- [ ] Test annual vs monthly billing cycles

#### 2. Downgrade Testing:
- [ ] Paid tier user downgrades to free tier
- [ ] Verify Stripe subscription cancellation
- [ ] Verify access retained until period end
- [ ] Test edge case: already on target tier

#### 3. PAYG Credit Testing:
- [ ] User reaches message limit
- [ ] PAYG modal opens automatically
- [ ] Purchase different credit packages
- [ ] Verify credits added to account
- [ ] Test continued chat after purchase

#### 4. Error Handling:
- [ ] Invalid tier names
- [ ] Missing authentication
- [ ] Stripe API failures
- [ ] Database connection issues

### Integration Testing:

#### 1. Chat Flow:
```bash
# Test message limit exceeded
1. Send messages until limit reached
2. Verify 429 error with usage details
3. Verify PAYG modal auto-opens
4. Purchase credits
5. Verify can continue chatting
```

#### 2. Pricing Flow:
```bash
# Test upgrade/downgrade
1. Navigate to pricing page
2. Verify current plan highlighted
3. Click upgrade/downgrade buttons
4. Verify subscription changes
5. Verify new limits applied
```

## Security Considerations

### Authentication:
- All edge functions verify JWT tokens
- User ID extracted from authenticated session
- No direct user input for sensitive operations

### Authorization:
- Users can only modify their own subscriptions
- Stripe customer validation prevents cross-user access
- Database RLS policies enforce user isolation

### Data Validation:
- Tier names validated against allowed values
- Billing cycles restricted to monthly/annual
- Credit packages validated against predefined options

## Monitoring & Logging

### Edge Function Logs:
- Detailed step-by-step logging in all functions
- Error context preserved for debugging
- Stripe operation tracking

### Key Metrics to Monitor:
- Upgrade/downgrade success rates
- PAYG credit purchase volumes
- Usage limit hit frequency
- Payment failure rates

## Future Enhancements

### Immediate:
- [ ] Full Stripe Elements integration for real payments
- [ ] Email notifications for subscription changes
- [ ] Proration calculations display

### Medium-term:
- [ ] Downgrade between paid tiers support
- [ ] Subscription pause/resume functionality
- [ ] Usage analytics dashboard

### Long-term:
- [ ] Team/enterprise plans
- [ ] Custom credit packages
- [ ] Referral credit system

## Configuration Required

### Stripe Setup:
1. Create price IDs for subscription tiers:
   - `price_reserved_seat_monthly`
   - `price_reserved_seat_annual`
   - `price_vip_table_monthly`
   - `price_vip_table_annual`

2. Configure webhook endpoints:
   - Payment intent succeeded
   - Subscription updated
   - Invoice payment succeeded

### Environment Variables:
- `STRIPE_SECRET_KEY` - Stripe API key
- `STRIPE_WEBHOOK_SECRET` - Webhook signature verification

## Deployment Checklist

- [ ] Deploy new edge functions
- [ ] Update Stripe webhook configuration
- [ ] Test payment flows in Stripe test mode
- [ ] Verify database functions working
- [ ] Monitor error logs after deployment
- [ ] Test user flows end-to-end

## Support Documentation

### User-Facing:
- Upgrade/downgrade process explanation
- PAYG credit system documentation
- Billing cycle information
- Cancellation policy

### Developer:
- Edge function API documentation
- Database schema changes
- Error code reference
- Monitoring setup guide 