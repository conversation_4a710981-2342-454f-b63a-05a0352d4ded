# Implementation Plan

- [x] 1. Enable anonymous authentication in Supabase configuration

  - Configure Supabase dashboard to enable anonymous authentication
  - Update RLS policies to include anonymous users in existing tables
  - Test anonymous user creation and session management
  - _Requirements: 1.1, 1.2_

- [x] 2. Extend AuthContext with anonymous authentication methods

  - Add `isAnonymous` and `needsEmailUpgrade` state properties to AuthContext
  - Implement `signInAnonymously()` method using Supabase anonymous auth
  - Implement `upgradeToEmail(email: string)` method using Supabase account linking
  - Add automatic anonymous sign-in on first visit when no existing session
  - Update auth state change handler to detect anonymous users
  - _Requirements: 1.1, 1.2, 3.1, 3.2_

- [x] 3. Create EmailUpgradeModal component

  - Build modal component that prompts for email after message limit reached
  - Integrate with AuthContext's `upgradeToEmail` method
  - Handle loading states and error scenarios during upgrade process
  - Style modal to match existing design system
  - Add form validation for email input
  - _Requirements: 2.3, 3.1, 3.4_

- [x] 4. Add anonymous authentication API endpoints

  - Create `/api/auth/anonymous-signin` endpoint in `supabase/functions/api/index.ts`
  - Create `/api/auth/upgrade-to-email` endpoint for account linking
  - Implement proper error handling and logging for both endpoints
  - Add rate limiting for anonymous user creation
  - Test endpoints with various scenarios (success, failure, edge cases)
  - _Requirements: 1.1, 3.2, 3.3_

- [x] 5. Integrate anonymous users with existing usage tracking system

  - Verify anonymous users work with existing `UsageContext` and usage API
  - Test message counting and quota enforcement for anonymous users
  - Ensure anonymous users can purchase PAYG credits through existing system
  - Verify usage data persists through email upgrade process
  - Test edge cases like multiple anonymous sessions
  - _Requirements: 2.1, 2.2, 5.3, 5.4, 6.1, 6.2_

- [x] 6. Implement message limit enforcement for anonymous users

  - Add logic to track anonymous user message count (3 message limit)
  - Display EmailUpgradeModal when anonymous user reaches 3 messages
  - Prevent additional messages until email is provided
  - Integrate with existing chat flow in Chat.tsx component
  - Test message blocking and upgrade prompt timing
  - _Requirements: 2.2, 2.3, 2.4_

- [ ] 7. Update existing PasswordSetupModal for deferred password creation

  - Modify PasswordSetupModal to handle email-only users returning to app
  - Add logic to detect users with email but no password on page load/navigation
  - Ensure modal blocks app access until password is created
  - Test cross-device scenarios and session handling
  - Verify integration with existing `hasPassword` metadata system
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [ ] 8. Test data persistence throughout authentication journey

  - Create test scenarios for anonymous → email → password upgrade flow
  - Verify conversation data persists through all authentication stages
  - Test usage quota preservation during account upgrades
  - Verify PAYG credit purchases work at each authentication stage
  - Test credit history persistence through all upgrades
  - _Requirements: 5.1, 5.2, 5.5, 6.3, 6.4, 7.1, 7.2, 7.3, 7.4, 7.5_

- [ ] 9. Update application entry points for automatic anonymous authentication

  - Modify main app initialization to automatically sign in anonymous users
  - Update routing logic to handle anonymous users appropriately
  - Remove authentication barriers from public pages
  - Test first-visit experience and automatic sign-in flow
  - Verify no waiting lists or invite requirements block access
  - _Requirements: 1.3, 1.4_

- [ ] 10. Add comprehensive error handling and edge case management

  - Implement fallback strategies for anonymous auth failures
  - Handle offline scenarios and browser compatibility issues
  - Add proper error messages and user feedback for all upgrade failures
  - Test account linking edge cases and rollback scenarios
  - Implement proper cleanup for failed upgrade attempts
  - _Requirements: All requirements - error handling_

- [ ] 11. Create end-to-end integration tests
  - Write automated tests for complete anonymous → email → password flow
  - Test message limit enforcement and upgrade prompts
  - Verify data persistence and credit purchase functionality
  - Test cross-device access and session management
  - Create performance tests for anonymous user creation at scale
  - _Requirements: All requirements - comprehensive testing_
