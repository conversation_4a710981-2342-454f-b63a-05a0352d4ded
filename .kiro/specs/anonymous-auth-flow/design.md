# Design Document

## Overview

The anonymous authentication flow implements a progressive user onboarding system that removes barriers to entry while maintaining data persistence throughout the authentication journey. The system leverages Supabase's anonymous authentication capabilities and provides seamless upgrades from anonymous to email-based to full password-protected accounts.

## Existing Implementation Analysis

### Current Authentication System
The existing codebase already has a robust authentication foundation:

- **AuthContext**: Comprehensive auth management with sign-in, sign-up, Google OAuth, and password reset
- **PasswordSetupModal**: Already handles deferred password creation with `hasPassword` metadata tracking
- **UsageContext & UsageDisplay**: Complete usage tracking and quota management system
- **Supabase Integration**: Fully configured with RLS policies and user metadata handling
- **Payment System**: Existing PAYG credits and Stripe integration

### Reusable Components
1. **AuthContext**: Can be extended with anonymous auth methods
2. **PasswordSetupModal**: Already supports required password setup flow
3. **UsageContext**: Message counting and quota enforcement already implemented
4. **PaygCreditsModal**: Credit purchase system ready for anonymous users
5. **Database Schema**: Usage tracking tables and RLS policies exist

### New Components Needed
1. **Anonymous Auth Manager**: Auto sign-in on first visit
2. **EmailUpgradeModal**: Prompt for email after 3 messages
3. **Account Upgrade Service**: Link anonymous sessions to email accounts

## Architecture

### Authentication Flow States

```mermaid
stateDiagram-v2
    [*] --> Anonymous : First visit
    Anonymous --> EmailPrompt : 3 messages sent
    EmailPrompt --> EmailLinked : Email provided
    EmailLinked --> PasswordPrompt : Page refresh/return
    PasswordPrompt --> FullAccount : Password created
    FullAccount --> [*] : Complete
    
    Anonymous --> Purchase : Buy credits
    EmailLinked --> Purchase : Buy credits
    Purchase --> Anonymous : Return to anonymous
    Purchase --> EmailLinked : Return to email-linked
```

### System Components

1. **Anonymous Auth Manager**: Handles automatic anonymous sign-in on first visit
2. **Message Quota Tracker**: Monitors message usage and triggers upgrade prompts
3. **Account Upgrade Service**: Manages transitions between authentication states
4. **Password Setup Modal**: Handles deferred password creation
5. **Payment Integration**: Processes credit purchases at any authentication stage
6. **Data Persistence Layer**: Ensures conversation and quota data survives upgrades

## Components and Interfaces

### AuthContext Extensions

The existing AuthContext will be extended with new methods and state while preserving all current functionality:

```typescript
interface AuthContextType {
  // Existing properties (unchanged)
  user: User | null;
  session: Session | null;
  loading: boolean;
  
  // New properties for anonymous flow
  isAnonymous: boolean;
  needsEmailUpgrade: boolean;
  
  // New methods for anonymous flow
  signInAnonymously: () => Promise<{ error: any }>;
  upgradeToEmail: (email: string) => Promise<{ error: any }>;
  
  // Existing methods (unchanged)
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signInWithGoogle: (redirectUrl?: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ error: any }>;
}
```

**Note**: Message counting and quota management will continue to use the existing `UsageContext` system, which already handles all usage tracking, limits, and credit purchases.

### Message Quota Integration

**Reuse Existing System**: The current `UsageContext` and backend API already provide comprehensive quota management:

- **Usage Tracking**: Automatic message counting via database triggers
- **Limit Enforcement**: API returns 429 errors when limits exceeded
- **Credit System**: PAYG credits already work with any authenticated user
- **Real-time Updates**: Usage data refreshes automatically

No new quota service needed - anonymous users will seamlessly integrate with existing usage tracking once authenticated.

### Account Upgrade Service

```typescript
interface AccountUpgradeService {
  upgradeAnonymousToEmail(userId: string, email: string): Promise<{ error: any }>;
  // Password setup reuses existing PasswordSetupModal and AuthContext.setupPassword
  // Data transfer handled automatically by Supabase when linking accounts
}
```

**Leverage Existing Components**:
- **Password Setup**: Existing `PasswordSetupModal` already handles required password creation
- **User Metadata**: Current system already tracks `hasPassword` and authentication state
- **Data Persistence**: Supabase account linking preserves all user data automatically

### Email Upgrade Modal Component

```typescript
interface EmailUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEmailSubmitted: (email: string) => Promise<void>;
  messageCount: number;
  isRequired: boolean;
}
```

## Data Models

### User Metadata Extensions

Minimal extensions to existing user metadata:

```typescript
interface UserMetadata {
  // Existing fields (unchanged)
  full_name?: string;
  hasPassword?: boolean;
  
  // New field for anonymous flow
  isAnonymous?: boolean;
  emailUpgradeDate?: string;
}
```

### Existing Database Schema (Reused)

The current database already includes all necessary tables:
- **User usage tracking**: Existing tables handle message counts and limits
- **Credit transactions**: Current PAYG system works with any authenticated user
- **Subscription management**: Existing tier and quota system supports anonymous users

**No new database tables required** - anonymous users integrate seamlessly with existing schema once authenticated.

## Error Handling

### Anonymous Authentication Failures

- **Fallback Strategy**: If anonymous auth fails, show a minimal error message and retry
- **Offline Handling**: Cache anonymous session locally and sync when online
- **Browser Compatibility**: Graceful degradation for browsers without localStorage support

### Account Upgrade Failures

- **Email Linking Errors**: Preserve anonymous session and allow retry
- **Password Setup Errors**: Allow user to skip and retry later
- **Data Transfer Errors**: Implement rollback mechanism to preserve original anonymous account

### Payment Processing Errors

- **Stripe Integration**: Handle payment failures gracefully without losing user session
- **Credit Application**: Ensure credits are applied even if UI update fails
- **Transaction Logging**: Maintain audit trail for all payment attempts

## Testing Strategy

### Unit Tests

1. **Anonymous Auth Manager**
   - Test automatic sign-in on first visit
   - Test session persistence across page reloads
   - Test fallback behavior on auth failures

2. **Message Quota Tracker**
   - Test message counting accuracy
   - Test limit enforcement
   - Test quota persistence during upgrades

3. **Account Upgrade Service**
   - Test email linking functionality
   - Test password setup process
   - Test data preservation during upgrades

### Integration Tests

1. **End-to-End User Journey**
   - Anonymous user → Email upgrade → Password setup
   - Data persistence throughout the flow
   - Credit purchases at each stage

2. **Cross-Device Scenarios**
   - Anonymous user on Device A, email upgrade on Device B
   - Password setup enabling cross-device access
   - Session synchronization

### Performance Tests

1. **Anonymous Auth Speed**
   - Measure time to anonymous sign-in
   - Test with high concurrent user loads
   - Optimize for mobile network conditions

2. **Data Migration Performance**
   - Test upgrade performance with large conversation histories
   - Measure database query efficiency
   - Test concurrent upgrade scenarios

## Security Considerations

### Anonymous User Data Protection

- Anonymous sessions are temporary but should be treated with same security standards
- Implement rate limiting to prevent abuse of anonymous accounts
- Ensure anonymous user data is properly cleaned up after account upgrades

### Email Verification

- Email addresses collected during upgrade should be validated
- Implement email verification flow for enhanced security
- Prevent email enumeration attacks

### Password Security

- Enforce strong password requirements during setup
- Implement secure password reset flow
- Use Supabase's built-in password hashing and validation

### Payment Security

- All payment processing through Stripe's secure APIs
- Never store payment card details locally
- Implement proper PCI compliance measures
- Secure webhook handling for payment confirmations

## Implementation Notes

### Supabase Configuration

- Enable anonymous authentication in Supabase dashboard
- Update existing RLS policies to include anonymous users
- Extend current user metadata handling for `isAnonymous` flag
- Configure email templates for upgrade notifications

### Database Schema Changes

**No new tables required** - leverage existing infrastructure:
- Current usage tracking tables work with anonymous users
- Existing credit and subscription systems support any authenticated user
- RLS policies need minor updates to include anonymous users

### Frontend State Management

- **Minimal Changes**: Extend existing AuthContext with 2-3 new methods
- **Reuse Components**: PasswordSetupModal, UsageDisplay, PaygCreditsModal work as-is
- **New Components**: Only EmailUpgradeModal and anonymous auth logic needed
- **Preserve UX**: All existing flows and UI remain unchanged

### Backend Services

**All API changes will be implemented in `supabase/functions/api/index.ts`**:

- **New Auth Routes**: Add `/auth/anonymous-signin` and `/auth/upgrade-to-email` endpoints
- **Extend Usage Routes**: Existing `/usage/check-limits` already supports any authenticated user
- **Reuse Payment System**: Current `/billing/purchase-credits` works with anonymous users once authenticated
- **Leverage Current Infrastructure**: All existing logging, error handling, and rate limiting apply
- **Minimal Changes**: Most account linking handled by Supabase's built-in functionality

**New API Endpoints to Add**:
```typescript
// POST /api/auth/anonymous-signin
// POST /api/auth/upgrade-to-email
```

**Existing Endpoints (No Changes)**:
```typescript
// POST /api/usage/check-limits - already works with any authenticated user
// POST /api/billing/purchase-credits - already works with any authenticated user
// POST /api/chat - already works with any authenticated user
```