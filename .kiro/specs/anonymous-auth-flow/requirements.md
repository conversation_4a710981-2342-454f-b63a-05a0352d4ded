# Requirements Document

## Introduction

This feature implements a seamless user onboarding flow that starts with anonymous authentication and progressively upgrades users to full accounts. The system removes barriers to entry by allowing immediate access while preserving user data throughout the authentication journey. Users begin anonymously, are prompted for email after reaching message limits, and can optionally set passwords for enhanced security and cross-device access.

## Requirements

### Requirement 1

**User Story:** As a new visitor, I want to immediately access the app without any signup barriers, so that I can start using the service right away.

#### Acceptance Criteria

1. WHEN a user visits the app for the first time THEN the system SHALL automatically sign them in using Supabase Anonymous Auth
2. WHEN anonymous authentication occurs THEN the system SHALL generate a unique auth.uid and store it in browser storage
3. WHEN the user interacts with the app THEN all conversations and usage data SHALL be tied to their anonymous auth.uid
4. WHEN the app loads THEN there SHALL be no waiting list, invite requirement, or signup form blocking access

### Requirement 2

**User Story:** As an anonymous user, I want to send messages up to a limit before being asked for more information, so that I can evaluate the service before committing.

#### Acceptance Criteria

1. WHEN an anonymous user sends messages THEN the system SHALL track their message count against their auth.uid
2. WHEN an anonymous user has sent fewer than 3 messages THEN they SHALL be able to continue sending messages without interruption
3. WHEN an anonymous user reaches 3 messages THEN the system SHALL display a prompt requesting their email address
4. WHEN the email prompt is shown THEN the user SHALL NOT be able to send additional messages until providing an email

### Requirement 3

**User Story:** As an anonymous user who has reached the message limit, I want to provide my email and continue chatting seamlessly, so that my conversation flow isn't disrupted.

#### Acceptance Criteria

1. WHEN a user submits their email address THEN the system SHALL link the current anonymous session to a new email-based account
2. WHEN the email linking occurs THEN the system SHALL use Supabase's auth.admin.updateUserById or equivalent functionality
3. WHEN the account upgrade happens THEN no password SHALL be required at this stage
4. WHEN the email is successfully linked THEN the user SHALL continue chatting without session interruption
5. WHEN the upgrade completes THEN all previous conversation data and usage SHALL remain accessible

### Requirement 4

**User Story:** As a user with an email-only account, I want to be prompted to create a password when I return to the app, so that my account is secure and accessible across devices.

#### Acceptance Criteria

1. WHEN a user with email but no password refreshes the page THEN the system SHALL prompt them to create a password
2. WHEN a user with email but no password navigates away and returns THEN the system SHALL prompt them to create a password before allowing app access
3. WHEN the password setup prompt is shown THEN the user SHALL NOT be able to access the app until completing password creation
4. WHEN a password is successfully created THEN the user SHALL be able to access their account from any device

### Requirement 5

**User Story:** As a user progressing through the authentication stages, I want all my data to be preserved, so that I don't lose my conversations or usage history.

#### Acceptance Criteria

1. WHEN a user transitions from anonymous to email-based account THEN all conversation data SHALL be preserved
2. WHEN a user transitions from email-only to full account THEN all conversation data SHALL be preserved
3. WHEN a user upgrades their account THEN their message quota usage SHALL be maintained
4. WHEN account upgrades occur THEN the message limit for free users SHALL remain 10 messages per month
5. WHEN quota calculations are performed THEN previously used quota SHALL be included in the total

### Requirement 6

**User Story:** As a user at any authentication stage, I want to purchase additional message credits, so that I can continue using the service beyond the free limit.

#### Acceptance Criteria

1. WHEN an anonymous user wants to purchase credits THEN the system SHALL allow payment processing linked to their auth.uid
2. WHEN an email-only user wants to purchase credits THEN the system SHALL allow payment processing without requiring a password
3. WHEN credits are purchased THEN they SHALL be linked to the user's current auth.uid
4. WHEN payment processing occurs THEN it SHALL be handled via Stripe integration
5. WHEN a user later creates a password THEN their top-up history SHALL remain accessible
6. WHEN credits are applied THEN they SHALL increase the user's available message quota

### Requirement 7

**User Story:** As a user with purchased credits, I want my payment history to persist through all account upgrades, so that I maintain access to my purchased services.

#### Acceptance Criteria

1. WHEN a user purchases credits in anonymous mode THEN the purchase SHALL be linked to their auth.uid
2. WHEN an anonymous user upgrades to email-based account THEN their credit history SHALL transfer seamlessly
3. WHEN an email-only user creates a password THEN their credit history SHALL remain accessible
4. WHEN credit transactions occur THEN they SHALL be stored with the user's auth.uid as the primary identifier
5. WHEN users view their account THEN they SHALL see their complete purchase history regardless of authentication stage