---
description: 
globs: 
alwaysApply: false
---
## **Header Component Styling Standards**

### **Background & Visual Effects**
- **Use semi-transparent background with backdrop blur for modern glass effect**
  - `bg-noir-charcoal/95 backdrop-blur-sm` for header background
  - `border-noir-amber/20` for subtle border styling
  - Add `header-deco` class for Art Deco styling elements

```typescript
// ✅ DO: Modern glass effect with Art Deco styling
<header className="bg-noir-charcoal/95 backdrop-blur-sm border-b border-noir-amber/20 py-4 px-6 header-deco">

// ❌ DON'T: Solid background without visual depth
<header className="bg-noir-charcoal border-b border-noir-amber/30">
```

### **Button Styling Consistency**
- **All buttons must use `font-source-sans` and `transition-all duration-300`**
- **Hover effects should use `hover:bg-noir-amber/20` for consistent interaction**
- **Maintain color hierarchy with proper contrast ratios**

```typescript
// ✅ DO: Consistent button styling with transitions
<Button className="font-source-sans transition-all duration-300 hover:bg-noir-amber/20">

// ❌ DON'T: Missing font family or transitions
<Button className="hover:bg-noir-charcoal/50">
```

### **Primary Action Button Standards**
- **Use `bg-noir-shadow` background with `border border-noir-amber`**
- **Text color must be `text-noir-amber` for proper contrast**
- **Hover state: `hover:bg-noir-amber hover:text-noir-charcoal`**
- **Include `art-deco-button` class and smooth transitions**

```typescript
// ✅ DO: Primary button with noir styling and proper contrast
<Button className="bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300">

// ❌ DON'T: Old amber background or missing border
<Button className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber">
```

### **Secondary Action Button Standards**
- **Use `bg-noir-shadow` background with `border border-noir-amber/50`**
- **Text color must be `text-noir-amber` with `art-deco-button` class**
- **Hover state: `hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber`**
- **Include smooth transitions: `transition-all duration-300`**

```typescript
// ✅ DO: Secondary button with proper noir styling and smooth transitions
<Button className="bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button">

// ❌ DON'T: Missing background or inadequate hover states
<Button variant="outline" className="border-noir-amber/50 text-noir-amber hover:bg-noir-amber/10">
```

### **Dropdown Menu Styling**
- **DropdownMenuContent**: `bg-noir-charcoal border-noir-amber/30 text-noir-cream`
- **DropdownMenuItem**: `text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber`
- **DropdownMenuSeparator**: `bg-noir-amber/30`

```typescript
// ✅ DO: Consistent dropdown styling with proper contrast
<DropdownMenuContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream">
  <DropdownMenuItem className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">

// ❌ DON'T: Inconsistent or missing hover/focus states
<DropdownMenuItem className="hover:bg-noir-amber/20 focus:bg-noir-amber/20">
```

### **Input & Form Element Accessibility**
- **ALL input elements must meet WCAG AA contrast standards (4.5:1 ratio)**
- **Use enhanced contrast colors for maximum accessibility**
- **Standard input styling should use the improved contrast color palette**

```typescript
// ✅ DO: High-contrast input styling (automatically applied via UI components)
<Input /> // Uses noir-input-bg, noir-input-text, noir-input-placeholder
<Textarea /> // Uses noir-input-bg, noir-input-text, noir-input-placeholder

// ❌ DON'T: Override with lower contrast colors
<Input className="bg-noir-shadow text-noir-cream placeholder:text-noir-cream/50" />
```

### **Enhanced Contrast Color Palette**
- **Background**: `noir-input-bg` (#1f1f1f) - Darker for better contrast
- **Text**: `noir-input-text` (#ffffff) - Pure white for maximum readability
- **Placeholder**: `noir-input-placeholder` (#b8b8b8) - Meets 4.5:1 contrast ratio
- **Border**: `noir-input-border` (#4a4a4a) - Meets 3:1 ratio for UI components
- **Focus**: `noir-input-focus` (#e6c547) - Brighter amber for focus states

### **Navigation & Interaction**
- **Consistent spacing and alignment across all header elements**
- **Proper focus management with visible focus indicators**
- **Smooth transitions for all interactive elements**

### **Cross-References**
- See [react.mdc](mdc:.cursor/rules/react.mdc) for general React component patterns
- See [cursor_rules.mdc](mdc:.cursor/rules/cursor_rules.mdc) for rule formatting standards
