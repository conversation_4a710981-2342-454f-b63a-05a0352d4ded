
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export const useBetaAccess = () => {
  const [hasBetaAccess, setHasBetaAccess] = useState(true); // Everyone has access now
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    // Since we're removing beta access restrictions, everyone has access
    setHasBetaAccess(true);
    setIsLoading(false);
  }, [user]);

  return { hasBetaAccess, isLoading };
};
