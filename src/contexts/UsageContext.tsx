import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { supabase } from '@/integrations/supabase/client';

interface SubscriptionInfo {
  tier: string;
  status: string;
  messages_used: number;
  messages_limit: number;
  messages_limit_actual?: number;  // Internal enforcement limit (optional for backward compatibility)
  payg_credits_remaining: number;
  period_start: string;
  period_end: string;
  is_anonymous?: boolean;  // Flag indicating if user is anonymous
}

interface UsageContextType {
  usageData: SubscriptionInfo | null;
  loading: boolean;
  error: string | null;
  refreshUsage: () => Promise<void>;
  lastRefresh: Date | null;
}

const UsageContext = createContext<UsageContextType | undefined>(undefined);

export function useUsage() {
  const context = useContext(UsageContext);
  if (context === undefined) {
    throw new Error('useUsage must be used within a UsageProvider');
  }
  return context;
}

interface UsageProviderProps {
  children: ReactNode;
}

export function UsageProvider({ children }: UsageProviderProps) {
  const { user } = useAuth();
  const [usageData, setUsageData] = useState<SubscriptionInfo | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  const refreshUsage = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);
    
    try {
      console.log('🔄 UsageContext: Refreshing usage data for user:', {
        userId: user.id,
        isAnonymous: user.user_metadata?.is_anonymous,
        email: user.email
      });
      
      const { data, error } = await supabase.functions.invoke('api/usage/check-limits', {
        method: 'POST',
        body: { action_type: 'message' }
      });

      console.log('📊 UsageContext: API Response:', { data, error });

      if (error) {
        console.error('❌ UsageContext: API Error:', error);
        throw error;
      }

      if (data?.subscription_info) {
        console.log('✅ UsageContext: Setting usage data:', {
          ...data.subscription_info,
          isAnonymous: data.subscription_info.is_anonymous
        });
        setUsageData(data.subscription_info);
        setLastRefresh(new Date());
      } else {
        console.warn('⚠️ UsageContext: No subscription_info in response:', data);
        throw new Error('No subscription information received from API');
      }
    } catch (error) {
      console.error('💥 UsageContext: Error refreshing usage:', error);
      setError(error instanceof Error ? error.message : 'Failed to load usage data');
    } finally {
      setLoading(false);
    }
  };

  // Initial load and user change effect
  useEffect(() => {
    if (user) {
      refreshUsage();
    } else {
      setUsageData(null);
      setError(null);
      setLastRefresh(null);
    }
  }, [user]);

  // Auto-refresh every 2 minutes when component is active
  useEffect(() => {
    if (!user) return;
    
    const interval = setInterval(() => {
      refreshUsage();
    }, 120000); // 2 minutes

    return () => clearInterval(interval);
  }, [user]);

  const value: UsageContextType = {
    usageData,
    loading,
    error,
    refreshUsage,
    lastRefresh,
  };

  return (
    <UsageContext.Provider value={value}>
      {children}
    </UsageContext.Provider>
  );
} 