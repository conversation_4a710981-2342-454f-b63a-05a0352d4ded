import React from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { UsageProvider } from "@/contexts/UsageContext";
import Index from "./pages/Index";
import Auth from "./pages/Auth";
import ResetPassword from "./pages/ResetPassword";
import Pricing from "./pages/Pricing";
import MyConversations from "./pages/MyConversations";
import Chat from "./pages/Chat";
import AccountSettings from "./pages/AccountSettings";
import CharacterDetail from "./components/CharacterDetail";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";
import WaitlistConfirmation from "./pages/WaitlistConfirmation";
import AnonymousUsageTestPage from "./pages/AnonymousUsageTestPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <UsageProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/conversations" element={<MyConversations />} />
              <Route path="/chat/:characterId" element={<Chat />} />
              <Route path="/chat" element={<Chat />} />
              <Route path="/account" element={<AccountSettings />} />
              <Route path="/about/:characterId" element={<CharacterDetail />} />
              <Route path="/waitlist-confirmation" element={<WaitlistConfirmation />} />
              <Route path="/test-anonymous-usage" element={<AnonymousUsageTestPage />} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
              <Route path="/unauthorized" element={<Unauthorized />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </UsageProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
