import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const NotFound = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-noir-black flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-noir-amber mb-4">404</h1>
          <h2 className="text-2xl font-semibold text-noir-cream mb-2">
            Page Not Found
          </h2>
          <p className="text-noir-cream/70">
            The page you're looking for has vanished into the shadows of the night.
          </p>
        </div>
        
        <div className="space-y-4">
          <Button
            onClick={handleGoHome}
            className="w-full bg-noir-amber text-noir-black hover:bg-noir-amber/90 font-semibold"
          >
            Go Home
          </Button>
          
          <Button
            onClick={handleGoBack}
            variant="outline"
            className="w-full  hover:bg-noir-amber/20 hover:border-noir-warm-amber focus:bg-noir-amber/20 focus:border-noir-warm-amber
             bg-noir-amber/30 border-noir-warm-amber text-noir-black"
          >
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
