import { useState, useEffect } from 'react';
import {  useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import PasswordSetupModal from '@/components/PasswordSetupModal';

const ResetPassword = () => {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    setIsProcessing(true);

    const checkUser = async () => {
      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        
        if (error || !user) {
          navigate('/unauthorized');
          return;
        }
        
      } catch (error) {
        navigate('/unauthorized');
      } finally {
        setIsProcessing(false);
      }
    };

    checkUser();
  }, [navigate]);

  if (isProcessing) {
    return (
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-noir-charcoal border border-noir-amber/30 rounded-lg p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-noir-amber mx-auto mb-4"></div>
          <p className="text-noir-cream">Processing reset link...</p>
        </div>
      </div>
    );
  }

  return (
    <PasswordSetupModal
      isOpen={true}
      onClose={() => {}}
      userEmail={''}
      isRequired={true}
      onSuccess={() => {
        // Navigate to index page on successful password setup
        navigate('/');
      }}
    />
  );
};

export default ResetPassword; 