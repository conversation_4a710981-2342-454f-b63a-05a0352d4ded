import React, { useState, useEffect } from 'react';
import { useLocation, useSearchParams, useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import CharacterCard from '@/components/CharacterCard';
import SubscriptionModal from '@/components/SubscriptionModal';
import WaitlistModal from '@/components/WaitlistModal';
import ResetPasswordModal from '@/components/ResetPasswordModal';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useCharacters, type Character } from '@/hooks/useCharacters';
import { Clock, Loader2 } from 'lucide-react';
import UsageDisplay from '@/components/UsageDisplay';

const Index = () => {
  const [selectedCharacter, setSelectedCharacter] = useState<Character | null>(null);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const [showResetModal, setShowResetModal] = useState(false);
  const [isSigningIn, setIsSigningIn] = useState(false);
  const { user, signInAnonymously } = useAuth();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  // Fetch characters from Supabase
  const { data: characters, isLoading, error } = useCharacters();

  // Handle password reset flow from email links
  useEffect(() => {
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');
    const type = searchParams.get('type');
    
    if (accessToken && refreshToken && type === 'recovery') {
      setShowResetModal(true);
    }
  }, [searchParams]);

  // Handle showWaitlistFor from About page navigation
  useEffect(() => {
    const showWaitlistFor = location.state?.showWaitlistFor;
    if (showWaitlistFor && !user) {
      setSelectedCharacter(showWaitlistFor);
      setShowWaitlistModal(true);
      // Clear the state to prevent re-triggering
      window.history.replaceState({}, document.title);
    }
  }, [location.state, user]);

  const handleSubscribe = async (plan: 'basic' | 'premium') => {
    console.log('Subscribe to plan:', plan);
    setShowSubscriptionModal(false);
    
    // Reset selected character
    setSelectedCharacter(null);
  };

  const handleReserveClick = async () => {
    if (!user) {
      console.log('User not authenticated, signing in anonymously...');
      setIsSigningIn(true);
      
      try {
        const { error } = await signInAnonymously();
        if (error) {
          console.error('Anonymous sign-in failed:', error);
          // Fallback to waitlist modal if anonymous sign-in fails
          setShowWaitlistModal(true);
          setIsSigningIn(false);
          return;
        }
        
        console.log('Anonymous sign-in successful');
        // User will now see their usage and can interact with characters
      } catch (error) {
        console.error('Error during anonymous sign-in:', error);
        setShowWaitlistModal(true);
      }
      
      setIsSigningIn(false);
    } else {
      setShowSubscriptionModal(true);
    }
  };

  const handleSignUpClick = () => {
    navigate('/auth?mode=signup');
  };

  // Handle error state - only show error in character section
  const renderCharacterSection = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-[300px]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="w-8 h-8 text-noir-amber animate-spin" />
            <p className="text-noir-cream/70">Loading characters...</p>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex items-center justify-center min-h-[300px]">
          <div className="text-center space-y-4">
            <p className="text-red-400">Failed to load characters</p>
            <p className="text-noir-cream/70 text-sm">{error.message}</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              Try Again
            </Button>
          </div>
        </div>
      );
    }

    if (!characters || characters.length === 0) {
      return (
        <div className="flex items-center justify-center min-h-[300px]">
          <div className="text-center space-y-4">
            <p className="text-noir-cream/70">No characters available</p>
            <Button 
              onClick={() => window.location.reload()} 
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              Refresh
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-5xl mx-auto">
        {characters.map((character) => (
          <CharacterCard
            key={character.id}
            character={character}
          />
        ))}
        
        {/* Coming Soon Cards */}
        {[].map((char, index) => (
          <div key={char.name} className="bg-noir-charcoal/50 border border-noir-amber/20 rounded-lg p-6 flex flex-col items-center justify-center text-center space-y-4 opacity-75 character-card-deco">
            <div className="w-20 h-20 rounded-full bg-noir-shadow/50 text-noir-amber/50 flex items-center justify-center text-3xl border-2 border-noir-amber/20">
              {char.avatar}
            </div>
            <div>
              <h3 className="font-hollywood text-lg text-noir-amber/70">{char.name}</h3>
              <p className="text-sm text-noir-cream/50">{char.title}</p>
              <p className="text-xs text-noir-amber mt-2">Coming Soon</p>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-noir-gradient">
      <Header />



      {/* Hero Section */}
      <main className="relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-noir-deep-charcoal/50"></div>
        
        <div className="relative z-10 max-w-6xl mx-auto px-6 py-16">
          {/* Welcome Section */}
          <div className="text-center mb-16 animate-fade-in art-deco-bg content-overlay">
            <h1 className="font-hollywood text-5xl lg:text-7xl font-bold text-noir-amber amber-glow mb-6 tracking-wide">
              Welcome to the Table
            </h1>
            <p className="text-xl lg:text-2xl text-noir-cream/80 max-w-3xl mx-auto leading-relaxed mb-8">
              Step into an intimate Hollywood restaurant booth and converse with AI versions of legendary screenwriting minds. Explore ideas, get feedback, and learn the craft from the masters themselves.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                onClick={handleReserveClick}
                disabled={isSigningIn}
                className={`font-semibold px-8 py-3 text-lg art-deco-button ${
                  isSigningIn
                    ? 'bg-noir-shadow border border-noir-amber/50 text-noir-amber/70 cursor-not-allowed'
                    : 'bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber'
                }`}
              >
                {isSigningIn 
                  ? 'Joining...' 
                  : !user ? 'Get Started' : 'Explore Premium'
                }
              </Button>
              <p className="text-noir-cream/60 text-sm">
                {isSigningIn 
                  ? 'Setting up your table...'
                  : user ? 'Welcome back to Hollywood Table' : 'Begin your conversations with legendary screenwriters'
                }
              </p>
            </div>
          </div>

          {/* Art Deco Section Divider */}
          <div className="art-deco-divider"></div>

          {/* Usage Display for authenticated users */}
          {user && (
            <div className="mb-16 max-w-md mx-auto">
              <UsageDisplay />
            </div>
          )}

          {/* Character Selection */}
          <div className="mb-16 art-deco-bg content-overlay">
            <div className="text-center mb-12">
              <h2 className="font-hollywood text-3xl lg:text-4xl font-semibold text-noir-amber mb-4">
                Choose Your Dinner Companion
              </h2>
              <p className="text-lg text-noir-cream/70 max-w-2xl mx-auto">
                Each character embodies decades of Hollywood wisdom and offers unique perspectives on the craft of storytelling.
              </p>
              {!user && !isSigningIn && (
                <div className="text-center mt-2">
                  <Button
                    variant="link"
                    onClick={handleReserveClick}
                    className="text-noir-amber font-medium hover:text-noir-warm-amber underline-offset-4 h-auto p-0 text-base"
                  >
                    Click any "Join for Dinner" button to start instantly
                  </Button>
                </div>
              )}
            </div>

            {renderCharacterSection()}
          </div>

          {/* Art Deco Section Divider */}
          <div className="art-deco-divider"></div>

          {/* Features Section */}
          <div className="bg-noir-charcoal/30 rounded-2xl p-8 border border-noir-amber/20 features-deco content-overlay">
            <h3 className="font-hollywood text-2xl font-semibold text-noir-amber text-center mb-8">
              The Hollywood Table Experience
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center space-y-3">
                <div className="w-16 h-16 bg-noir-burgundy rounded-full flex items-center justify-center mx-auto text-2xl character-avatar-frame">
                  🎬
                </div>
                <h4 className="font-semibold text-noir-cream">Authentic Conversations</h4>
                <p className="text-noir-cream/70 text-sm">
                  Deep, meaningful discussions about character development, plot structure, and the art of storytelling.
                </p>
              </div>
              <div className="text-center space-y-3">
                <div className="w-16 h-16 bg-noir-burgundy rounded-full flex items-center justify-center mx-auto text-2xl character-avatar-frame">
                  📚
                </div>
                <h4 className="font-semibold text-noir-cream">Expert Guidance</h4>
                <p className="text-noir-cream/70 text-sm">
                  Learn from AI characters trained on the wisdom and techniques of Hollywood's greatest screenwriters.
                </p>
              </div>
              <div className="text-center space-y-3">
                <div className="w-16 h-16 bg-noir-burgundy rounded-full flex items-center justify-center mx-auto text-2xl character-avatar-frame">
                  💎
                </div>
                <h4 className="font-semibold text-noir-cream">Premium Platform</h4>
                <p className="text-noir-cream/70 text-sm">
                  Professional-grade tool designed for serious screenwriters, directors, and film students.
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Modals */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        onSubscribe={handleSubscribe}
      />
      
      <WaitlistModal
        isOpen={showWaitlistModal}
        onClose={() => setShowWaitlistModal(false)}
        characterName={selectedCharacter?.name}
      />

      <ResetPasswordModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
      />
    </div>
  );
};

export default Index;
