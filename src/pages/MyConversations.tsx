
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useConversations } from '@/hooks/useConversations';
import { useAuth } from '@/contexts/AuthContext';
import { useBetaAccess } from '@/hooks/useBetaAccess';
import Header from '@/components/Header';
import { ArrowLeft, MessageCircle, Clock, Search } from 'lucide-react';
import { format } from 'date-fns';

const MyConversations = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { conversations, isLoading } = useConversations();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('recent');
  const [filter<PERSON>haracter, set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] = useState('all');

  // Get character avatar
  const getCharacterAvatar = (characterId: string) => {
    const avatars: Record<string, string> = {
      'max-sterling': '🎭',
      'danny-cross': '🔫',
      'ruby-cohen': '💕',
      'holly-winters': '❄️'
    };
    return avatars[characterId] || '🎬';
  };

  // Filter and sort conversations
  const filteredConversations = conversations
    .filter(conv => {
      const matchesSearch = conv.character_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (conv.last_message_preview && conv.last_message_preview.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesFilter = filterCharacter === 'all' || conv.character_id === filterCharacter;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      if (sortBy === 'recent') {
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      } else if (sortBy === 'oldest') {
        return new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
      } else {
        return a.character_name.localeCompare(b.character_name);
      }
    });

  const handleContinueChat = (conversation: any) => {
    navigate(`/chat/${conversation.character_id}`, { 
      state: { 
        continueConversation: {
          id: conversation.id,
          characterId: conversation.character_id,
          characterName: conversation.character_name
        }
      }
    });
  };

  // Check access permissions - only check if user is authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-noir-gradient">
        <Header />
        <div className="max-w-4xl mx-auto px-6 py-16">
          <Card className="bg-noir-charcoal border-noir-amber/30 p-8 text-center">
            <MessageCircle className="w-16 h-16 text-noir-amber mx-auto mb-4" />
            <h2 className="font-hollywood text-2xl font-semibold text-noir-amber mb-4">
              Conversation History
            </h2>
            <p className="text-noir-cream/80 mb-6">
              {!user 
                ? "Sign in to save and access your conversation history with Hollywood legends."
                : "Upgrade to Reserved or VIP membership to save your conversations and continue them anytime."
              }
            </p>
            <div className="flex gap-4 justify-center">
              <Button
                onClick={() => navigate(!user ? '/auth' : '/pricing')}
                className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
              >
                {!user ? 'Sign In' : 'Upgrade Membership'}
              </Button>
              <Button
                onClick={() => navigate('/')}
                variant="ghost"
                className="text-noir-amber hover:text-noir-warm-amber"
              >
                Back to Table
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-noir-gradient">
      <Header />
      
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/')}
              className="bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button font-source-sans"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Back to Table
            </Button>
            <div>
              <h1 className="font-hollywood text-3xl font-semibold text-noir-amber">
                My Conversations
              </h1>
              <p className="text-noir-cream/70 font-source-sans">
                Continue your discussions with Hollywood legends
              </p>
            </div>
          </div>
          <div className="bg-noir-amber text-noir-charcoal px-3 py-1 rounded-full text-sm font-semibold">
            Reserved Member
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-noir-cream/50 w-4 h-4" />
            <Input
              placeholder="Search conversations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-noir-input-bg border-noir-input-border text-noir-input-text placeholder:text-noir-input-placeholder focus:border-noir-input-focus font-source-sans transition-all duration-300"
            />
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-44 bg-noir-shadow border-noir-amber/30 text-noir-cream">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream">
              <SelectItem value="recent" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Most Recent</SelectItem>
              <SelectItem value="oldest" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Oldest</SelectItem>
              <SelectItem value="character" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">By Character</SelectItem>
            </SelectContent>
          </Select>
          <Select value={filterCharacter} onValueChange={setFilterCharacter}>
            <SelectTrigger className="w-44 bg-noir-shadow border-noir-amber/30 text-noir-cream">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream">
              <SelectItem value="all" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">All Characters</SelectItem>
              <SelectItem value="max-sterling" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Max Sterling</SelectItem>
              <SelectItem value="danny-cross" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Danny Cross</SelectItem>
              <SelectItem value="ruby-cohen" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Ruby Cohen</SelectItem>
              <SelectItem value="holly-winters" className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber">Holly Winters</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Conversations List */}
        {isLoading ? (
          <div className="space-y-4">
            {/* Loading skeleton cards */}
            {[...Array(3)].map((_, index) => (
              <Card key={index} className="bg-noir-charcoal border-noir-amber/30 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="w-12 h-12 rounded-full bg-noir-shadow animate-pulse border border-noir-amber/30"></div>
                    <div className="flex-1 min-w-0">
                      <div className="h-5 bg-noir-shadow animate-pulse rounded mb-2 w-1/3"></div>
                      <div className="h-4 bg-noir-shadow animate-pulse rounded mb-2 w-3/4"></div>
                      <div className="h-3 bg-noir-shadow animate-pulse rounded w-1/2"></div>
                    </div>
                  </div>
                  <div className="h-9 w-28 bg-noir-shadow animate-pulse rounded"></div>
                </div>
              </Card>
            ))}
            <div className="text-center py-8">
              <div className="w-8 h-8 border-2 border-noir-amber border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-noir-cream/70 font-source-sans">Loading your conversations...</p>
            </div>
          </div>
        ) : filteredConversations.length === 0 ? (
          <Card className="bg-noir-charcoal border-noir-amber/30 p-12 text-center">
            <MessageCircle className="w-16 h-16 text-noir-amber/50 mx-auto mb-4" />
            {conversations.length === 0 ? (
              <>
                <h3 className="font-hollywood text-xl text-noir-amber mb-4">
                  Start Your First Conversation
                </h3>
                <p className="text-noir-cream/70 mb-6">
                  Choose a Hollywood legend and begin your screenwriting journey
                </p>
                <Button
                  onClick={() => navigate('/')}
                  className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
                >
                  Choose a Character
                </Button>
              </>
            ) : (
              <>
                <h3 className="font-hollywood text-xl text-noir-amber mb-4">
                  No conversations found
                </h3>
                <p className="text-noir-cream/70">
                  Try adjusting your search or filter criteria
                </p>
              </>
            )}
          </Card>
        ) : (
          <div className="space-y-4">
            {filteredConversations.map((conversation) => (
              <Card key={conversation.id} className="bg-noir-charcoal border-noir-amber/30 p-6 hover:border-noir-amber/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="w-12 h-12 rounded-full bg-noir-burgundy text-noir-cream flex items-center justify-center text-xl font-bold border border-noir-amber">
                      {getCharacterAvatar(conversation.character_id)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="font-hollywood text-lg font-semibold text-noir-amber mb-1">
                        {conversation.character_name}
                      </h3>
                      {conversation.last_message_preview && (
                        <p className="text-noir-cream/80 text-sm line-clamp-2 mb-2">
                          "{conversation.last_message_preview}..."
                        </p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-noir-cream/60">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{format(new Date(conversation.updated_at), 'MMM d, h:mm a')}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MessageCircle className="w-3 h-3" />
                          <span>{conversation.message_count || 0} messages</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleContinueChat(conversation)}
                    className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
                  >
                    Continue Chat
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyConversations;
