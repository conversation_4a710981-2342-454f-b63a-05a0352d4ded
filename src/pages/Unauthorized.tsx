import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const Unauthorized = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleSignIn = () => {
    navigate('/auth');
  };

  return (
    <div className="min-h-screen bg-noir-black flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-6xl font-bold text-noir-amber mb-4">401</h1>
          <h2 className="text-2xl font-semibold text-noir-cream mb-2">
            Unauthorized Access
          </h2>
          <p className="text-noir-cream/70">
            You don't have permission to access this page. Please sign in with valid credentials.
          </p>
        </div>
        
        <div className="space-y-4">
          <Button
            onClick={handleSignIn}
            className="w-full bg-noir-amber text-noir-black hover:bg-noir-amber/90 font-semibold"
          >
            Sign In
          </Button>
          
          <Button
            onClick={handleGoHome}
            variant="outline"
            className="w-full  hover:bg-noir-amber/20 hover:border-noir-warm-amber focus:bg-noir-amber/20 focus:border-noir-warm-amber
             bg-noir-amber/30 border-noir-warm-amber text-noir-black"
          >
            Go Home
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Unauthorized; 