import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Check, Star, Clock, Loader2, CreditCard, X, AlertTriangle } from 'lucide-react';
import Header from '@/components/Header';
import WaitlistModal from '@/components/WaitlistModal';
import PaygCreditsModal from '@/components/PaygCreditsModal';
import UsageDisplay from '@/components/UsageDisplay';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';
import { SUBSCRIPTION_TIERS_DISPLAY, TIER_HIERARCHY, isUpgrade } from '@/model/pricing';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface SubscriptionData {
  subscribed: boolean;
  subscription_tier: string | null;
  subscription_end: string | null;
}

const Pricing = () => {
  const [isWaitlistOpen, setIsWaitlistOpen] = useState(false);
  const [isPaygModalOpen, setIsPaygModalOpen] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'annual'>('monthly');
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    subscribed: false,
    subscription_tier: null,
    subscription_end: null
  });
  const [loadingSubscription, setLoadingSubscription] = useState(false);
  const [loadingAction, setLoadingAction] = useState(false);
  const [showStripeRedirect, setShowStripeRedirect] = useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = useState(false);
  const [showUpgradeConfirmation, setShowUpgradeConfirmation] = useState(false);
  const [showDowngradeConfirmation, setShowDowngradeConfirmation] = useState(false);
  const [pendingTierChange, setPendingTierChange] = useState<{
    tierName: string;
    action: 'upgrade' | 'downgrade';
    billingCycle?: 'monthly' | 'annual';
  } | null>(null);

  // Block scrolling when overlay is showing
  useEffect(() => {
    if (showStripeRedirect) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to reset overflow when component unmounts
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showStripeRedirect]);

  // Check subscription status when user is authenticated
  useEffect(() => {
    if (user && !authLoading) {
      checkSubscriptionStatus();
    } else if (!user && !authLoading) {
      // Reset subscription data when user logs out
      setSubscriptionData({
        subscribed: false,
        subscription_tier: null,
        subscription_end: null
      });
      setLoadingSubscription(false);
    }
  }, [user, authLoading]);

  // Handle Stripe Checkout success/cancel on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const canceled = urlParams.get('canceled');
    const sessionId = urlParams.get('session_id');
    const paygSuccess = urlParams.get('payg_success');
    const paygCanceled = urlParams.get('payg_canceled');

    if (success === 'true' && sessionId) {
      // Subscription payment was successful, refresh subscription data
      if (user) {
        checkSubscriptionStatus();
      }
      toast({
        title: "Subscription activated!",
        description: "Your payment was processed successfully. Welcome to your new plan!",
        duration: 5000,
      });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (canceled === 'true') {
      // Subscription payment was canceled
      toast({
        title: "Payment canceled",
        description: "Your subscription upgrade was canceled. You can try again anytime.",
        variant: "destructive",
        duration: 3000,
      });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (paygSuccess === 'true') {
      // PAYG credit purchase was successful
      if (user) {
        checkSubscriptionStatus(); // Refresh to get updated credit balance
      }
      toast({
        title: "Credits purchased successfully!",
        description: "Your additional credits have been added to your account and are ready to use.",
        duration: 5000,
      });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (paygCanceled === 'true') {
      // PAYG credit purchase was canceled
      toast({
        title: "Credit purchase canceled",
        description: "Your credit purchase was canceled. You can try again anytime.",
        variant: "destructive",
        duration: 3000,
      });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [user]);

  const checkSubscriptionStatus = async () => {
    if (!user) return;
    
    setLoadingSubscription(true);
    try {
      const { data, error } = await supabase.functions.invoke('api/billing/subscription',{
        method: 'GET',
      });
      if (error) throw error;
      
      if (data) {
        setSubscriptionData(data);
      }
    } catch (error) {
      console.error('Error checking subscription:', error);
      // Default to free tier if check fails
      setSubscriptionData({
        subscribed: false,
        subscription_tier: "Sample the Table",
        subscription_end: null
      });
    } finally {
      setLoadingSubscription(false);
    }
  };

  const handleUpgradeConfirmation = (targetTier: string) => {
    setPendingTierChange({ tierName: targetTier, action: 'upgrade', billingCycle });
    setShowUpgradeConfirmation(true);
  };

  const handleDowngradeConfirmation = (targetTier: string, billingCycle: 'monthly' | 'annual' = 'monthly') => {
    setPendingTierChange({ tierName: targetTier, action: 'downgrade', billingCycle });
    setShowDowngradeConfirmation(true);
  };

  const handleUpgrade = async (targetTier: string) => {
    if (!user) return;
    
    setLoadingAction(true);
    setShowStripeRedirect(true);
    setShowUpgradeConfirmation(false);
    
    try {
      console.log('Starting upgrade process for tier:', targetTier);
      
      const { data, error } = await supabase.functions.invoke('api/billing/manage-subscription', {
        method: 'POST',
        body: {
          target_tier: targetTier,
          billing_cycle: billingCycle
        }
      });

      console.log('Upgrade response:', { data, error });

      if (error) {
        console.error('Supabase function error:', error);
        setShowStripeRedirect(false);
        toast({
          title: "Upgrade failed",
          description: error.message || "There was an error processing your upgrade. Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.checkout_url) {
        console.log('Redirecting to Stripe Checkout:', data.checkout_url);
        window.location.href = data.checkout_url;
      } else if (data.success) {
        // Subscription was updated successfully (existing subscription modification)
        setShowStripeRedirect(false);
        toast({
          title: "Subscription updated!",
          description: "Your subscription has been successfully updated.",
        });
        checkSubscriptionStatus();
      } else {
        console.error('Unexpected response format:', data);
        setShowStripeRedirect(false);
        toast({
          title: "Unexpected response",
          description: "The upgrade process returned an unexpected response. Please contact support.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error upgrading subscription:', error);
      setShowStripeRedirect(false);
      toast({
        title: "Network error",
        description: "There was a network error. Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingAction(false);
    }
  };

  const handleDowngrade = async (targetTier: string, billingCycle: 'monthly' | 'annual' = 'monthly') => {
    if (!user) return;
    
    setLoadingAction(true);
    setShowDowngradeConfirmation(false);
    
    try {
      const { data, error } = await supabase.functions.invoke('api/billing/manage-subscription', {
        method: 'POST',
        body: {
          target_tier: targetTier,
          billing_cycle: billingCycle
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        toast({
          title: "Downgrade failed",
          description: error.message || "There was an error processing your downgrade. Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.success) {
        toast({
          title: "Subscription updated!",
          description: data.message || "Your subscription has been successfully downgraded.",
        });
      }

      // Refresh subscription data
      checkSubscriptionStatus();
    } catch (error) {
      console.error('Error downgrading subscription:', error);
      toast({
        title: "Network error",
        description: "There was a network error. Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingAction(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!user) return;
    
    setLoadingAction(true);
    setShowCancelConfirmation(false);
    
    try {
      const { data, error } = await supabase.functions.invoke('api/billing/manage-subscription', {
        method: 'POST',
        body: {
          action: 'cancel_keep_credits'
        }
      });

      if (error) {
        console.error('Supabase function error:', error);
        toast({
          title: "Cancellation failed",
          description: error.message || "There was an error canceling your subscription. Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (data.success) {
        const title = data.previous_tier && data.current_tier 
          ? `Moved from ${data.previous_tier} to ${data.current_tier}`
          : "Subscription canceled";
        
        toast({
          title: title,
          description: data.message || "Your subscription has been canceled. You've been moved to the free tier but will keep your current usage until your next billing cycle.",
        });
      }

      // Refresh subscription data
      checkSubscriptionStatus();
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast({
        title: "Network error",
        description: "There was a network error. Please check your connection and try again.",
        variant: "destructive",
      });
    } finally {
      setLoadingAction(false);
    }
  };

  // Helper function to determine button text and action based on user's current tier
  const getButtonConfig = (tierName: string) => {
    if (!user) {
      if (tierName === "Sample the Table") {
        return {
          type: "single",
          text: "Start Free",
          action: () => navigate('/'),
          disabled: false
        };
      } else {
        return {
          type: "single",
          text: "Start Free & Upgrade",
          action: () => navigate('/'),
          disabled: false
        };
      }
    }

    const currentTier = subscriptionData.subscription_tier || "Sample the Table";
    
    if (currentTier === tierName) {
      return {
        type: "current",
        isCurrentPlan: true
      };
    }

    // Determine if this is an upgrade or downgrade using shared logic
    if (isUpgrade(currentTier, tierName)) {
      return {
        type: "single",
        text: "Upgrade",
        action: () => handleUpgradeConfirmation(tierName),
        disabled: false
      };
    } else {
      return {
        type: "single",
        text: "Downgrade",
        action: () => handleDowngradeConfirmation(tierName, billingCycle),
        disabled: false
      };
    }
  };

  // Generate pricing tiers with dynamic pricing based on billing cycle
  const pricingTiers = SUBSCRIPTION_TIERS_DISPLAY.map(tier => ({
    ...tier,
    price: billingCycle === 'monthly' ? tier.monthlyPrice : 
           tier.name === "Reserved Seat" ? "$12.49" : 
           tier.name === "VIP Table" ? "$29.17" : tier.monthlyPrice,
    originalPrice: tier.originalPrice && billingCycle === 'monthly' ? tier.originalPrice : null,
    period: tier.monthlyPrice !== "Free" ? "/month" : "",
  }));

  const testimonials = [
    {
      quote: "The dialogue coaching from Max Sterling transformed my screenplay. I finally understand how to write characters that feel real.",
      author: "Sarah Chen",
      title: "Screenwriter, 'Urban Nights'"
    },
    {
      quote: "Danny Cross taught me that action sequences need heart. My latest script sold after implementing his character-driven approach.",
      author: "Marcus Rodriguez",
      title: "Writer-Director"
    },
    {
      quote: "Having legendary screenwriters as mentors 24/7 is invaluable. The insights are pure gold.",
      author: "Jennifer Walsh",
      title: "Film Student, USC"
    }
  ];

  return (
    <div className="min-h-screen bg-noir-gradient">
      <Header />
      
      <div className="container mx-auto px-6 py-16">
        {/* Pricing Banner */}
        <div className="mb-12">
          <div className="bg-gradient-to-r from-noir-burgundy/20 via-noir-amber/10 to-noir-burgundy/20 border border-noir-amber/30 rounded-lg p-6 text-center">
            <div className="flex items-center justify-center mb-3">
              <Clock className="w-5 h-5 text-noir-amber mr-2" />
              <span className="text-noir-amber font-semibold text-lg">Launch Pricing</span>
            </div>
            <p className="text-noir-cream/90 text-lg">
              Special founder rates available now. Lock in these prices today.
            </p>
            <p className="text-noir-cream/70 text-sm mt-2">
              Start chatting with Hollywood's greatest screenwriting minds immediately
            </p>
          </div>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="font-hollywood text-5xl md:text-6xl font-bold text-noir-amber mb-6">
            Choose Your Seat at the Table
          </h1>
          <p className="text-xl text-noir-cream/80 max-w-3xl mx-auto mb-8">
            Learn screenwriting from Hollywood's greatest minds. Select the plan that fits your journey 
            from aspiring writer to industry professional.
          </p>
          {user && (
            <div className="flex flex-col items-center space-y-6 mb-8">
              <div className="bg-noir-shadow border border-noir-amber/30 rounded-lg p-4 max-w-md mx-auto">
                {loadingSubscription ? (
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="w-4 h-4 text-noir-amber animate-spin" />
                    <p className="text-noir-cream/80 text-sm">Loading subscription details...</p>
                  </div>
                ) : subscriptionData.subscription_tier ? (
                  <p className="text-noir-cream/80 text-sm">
                    Current Plan: <span className="text-noir-amber font-semibold">{subscriptionData.subscription_tier}</span>
                  </p>
                ) : (
                  <p className="text-noir-cream/80 text-sm">
                    Current Plan: <span className="text-noir-amber font-semibold">Sample the Table</span>
                  </p>
                )}
              </div>
              
              {/* Usage Display for authenticated users */}
              <div className="w-full max-w-md mx-auto">
                <UsageDisplay />
              </div>
            </div>
          )}
        </div>

        {/* Billing Toggle */}
        <div className="flex justify-center mb-12">
          <div className="bg-noir-shadow border border-noir-amber/30 rounded-lg p-1 flex relative">
            <button
              onClick={() => setBillingCycle('monthly')}
              disabled={loadingAction || loadingSubscription}
              className={`px-6 py-2 rounded-md transition-all duration-500 ease-in-out font-medium relative z-10 ${
                loadingAction || loadingSubscription
                  ? 'cursor-not-allowed opacity-50'
                  : billingCycle === 'monthly'
                  ? 'text-noir-charcoal'
                  : 'text-noir-cream hover:text-noir-amber'
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setBillingCycle('annual')}
              disabled={loadingAction || loadingSubscription}
              className={`px-6 py-2 rounded-md transition-all duration-500 ease-in-out font-medium relative z-10 ${
                loadingAction || loadingSubscription
                  ? 'cursor-not-allowed opacity-50'
                  : billingCycle === 'annual'
                  ? 'text-noir-charcoal'
                  : 'text-noir-cream hover:text-noir-amber'
              }`}
            >
              Annual
              
            </button>
            {/* Sliding background indicator */}
            <div 
              className={`absolute top-1 bottom-1 bg-noir-amber rounded-md transition-all duration-500 ease-in-out ${
                billingCycle === 'monthly' 
                  ? 'left-1 w-[calc(50%-0.125rem)]' 
                  : 'right-1 w-[calc(50%-0.125rem)]'
              }`}
            />
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {pricingTiers.map((tier, index) => {
            const buttonConfig = getButtonConfig(tier.name);
            const isCurrentPlan = user && subscriptionData.subscription_tier === tier.name;
            
            return (
              <Card
                key={index}
                className={`relative p-8 transition-all duration-300 hover:scale-105 flex flex-col h-full ${
                  loadingSubscription ? 'opacity-75' : ''
                } ${
                  tier.popular
                    ? 'bg-noir-burgundy/20 border-noir-amber shadow-xl ring-2 ring-noir-amber/30 lg:scale-105'
                    : isCurrentPlan
                    ? 'bg-noir-amber/10 border-noir-amber shadow-lg ring-1 ring-noir-amber/50'
                    : 'bg-noir-shadow border-noir-amber/20 hover:border-noir-amber/50'
                }`}
              >
                {tier.badge && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className={`px-4 py-1 text-xs font-bold rounded-full ${
                      isCurrentPlan 
                        ? 'bg-noir-amber text-noir-charcoal'
                        : 'bg-noir-amber text-noir-charcoal'
                    }`}>
                      {loadingSubscription ? 'LOADING...' : isCurrentPlan ? 'CURRENT PLAN' : tier.badge}
                    </span>
                  </div>
                )}

                <div className="text-center mb-8">
                  <div className="text-noir-amber/70 text-sm font-medium mb-2">{tier.tier}</div>
                  <h3 className="font-hollywood text-2xl font-semibold text-noir-amber mb-4">
                    {tier.name}
                  </h3>
                  
                  <div className="mb-4">
                    <div className={`text-lg text-noir-cream/50 line-through mb-1 transition-all duration-500 ease-in-out ${
                      tier.originalPrice && billingCycle === 'monthly' 
                        ? 'opacity-100 transform translate-y-0' 
                        : 'opacity-0 transform -translate-y-2'
                    }`}>
                      {tier.originalPrice}{tier.period}
                    </div>
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-noir-cream transition-all duration-500 ease-in-out transform">
                        {tier.price}
                      </span>
                      {tier.period && (
                        <span className="text-noir-cream/70 ml-1 transition-all duration-500 ease-in-out">
                          {tier.period}
                        </span>
                      )}
                    </div>
                    <div className={`text-sm text-noir-amber/80 mt-2 transition-all duration-500 ease-in-out ${
                      billingCycle === 'annual' && tier.annualPrice && tier.name !== 'Sample the Table'
                        ? 'opacity-100 transform translate-y-0' 
                        : 'opacity-0 transform translate-y-2'
                    }`}>
                      Billed annually: {tier.annualPrice}/year ({tier.annualSavings})
                    </div>
                  </div>
                  
                  <p className="text-noir-cream/60 text-sm">{tier.description}</p>
                </div>

                <div className="space-y-6 mb-8 flex-grow">
                  <div>
                    <ul className="space-y-3">
                      {tier.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start text-noir-cream/80 text-sm">
                          <Check className="w-4 h-4 text-noir-amber mr-3 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {tier.limitations.length > 0 && (
                    <div className="pt-4 border-t border-noir-amber/10">
                      <h4 className="text-noir-cream/50 text-xs font-medium mb-2 uppercase tracking-wide">
                        Requirements
                      </h4>
                      <ul className="space-y-2">
                        {tier.limitations.map((limitation, idx) => (
                          <li key={idx} className="text-noir-cream/40 text-sm">
                            • {limitation}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                <div className="mt-auto">
                  {buttonConfig.type === "current" ? (
                    <div className="space-y-3">
                      <div className="bg-noir-amber/10 border border-noir-amber/50 rounded-md p-3 text-center">
                        <span className="text-noir-amber font-medium text-sm">Your Current Plan</span>
                      </div>
                      {tier.name === "Sample the Table" ? (
                        <Button
                          onClick={() => setIsPaygModalOpen(true)}
                          disabled={loadingAction || loadingSubscription}
                          className="w-full text-xs bg-noir-shadow border border-noir-amber/30 text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal transition-all duration-300"
                        >
                          {loadingAction || loadingSubscription ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <>
                              <CreditCard className="w-3 h-3 mr-1" />
                              Buy Credits
                            </>
                          )}
                        </Button>
                      ) : (
                        <div className="grid grid-cols-2 gap-2">
                          <Button
                            onClick={() => setIsPaygModalOpen(true)}
                            disabled={loadingAction || loadingSubscription}
                            className="text-xs bg-noir-shadow border border-noir-amber/30 text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal transition-all duration-300"
                          >
                            {loadingAction || loadingSubscription ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <>
                                <CreditCard className="w-3 h-3 mr-1" />
                                Buy Credits
                              </>
                            )}
                          </Button>
                          <Button
                            onClick={() => setShowCancelConfirmation(true)}
                            disabled={loadingAction || loadingSubscription}
                            className="text-xs bg-noir-shadow border border-red-500/30 text-red-400 hover:bg-red-500 hover:text-white transition-all duration-300"
                            title="Cancel recurring billing but keep current plan benefits until period ends"
                          >
                            {loadingAction || loadingSubscription ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <>
                                <X className="w-3 h-3 mr-1" />
                                Cancel Billing
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <Button 
                      onClick={'action' in buttonConfig ? buttonConfig.action : () => {}}
                      disabled={('disabled' in buttonConfig ? buttonConfig.disabled : false) || loadingAction || loadingSubscription}
                      className={`w-full transition-all duration-300 ${
                        ('disabled' in buttonConfig ? buttonConfig.disabled : false) || loadingSubscription
                          ? 'bg-noir-charcoal border border-noir-amber/50 text-noir-amber/70 cursor-not-allowed'
                          : tier.buttonClass
                      }`}
                    >
                      {loadingAction ? (
                        <div className="flex items-center justify-center space-x-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Processing...</span>
                        </div>
                      ) : loadingSubscription ? (
                        <div className="flex items-center justify-center space-x-2">
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Loading...</span>
                        </div>
                      ) : (
                        'text' in buttonConfig ? buttonConfig.text : ''
                      )}
                    </Button>
                  )}
                </div>
              </Card>
            );
          })}
        </div>

        {/* Social Proof */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-8">
            <div className="flex items-center text-noir-amber">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 fill-current" />
              ))}
            </div>
            <span className="ml-3 text-noir-cream/80">Trusted by 1,000+ screenwriters</span>
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <h2 className="font-hollywood text-3xl font-semibold text-noir-amber text-center mb-12">
            What Our Beta Users Say
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-noir-shadow border-noir-amber/20 p-6">
                <div className="mb-4">
                  <div className="flex text-noir-amber mb-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-current" />
                    ))}
                  </div>
                  <p className="text-noir-cream/80 italic mb-4">"{testimonial.quote}"</p>
                </div>
                <div>
                  <div className="font-medium text-noir-amber">{testimonial.author}</div>
                  <div className="text-noir-cream/60 text-sm">{testimonial.title}</div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="text-center">
          <h2 className="font-hollywood text-3xl font-semibold text-noir-amber mb-8">
            Frequently Asked Questions
          </h2>
          <p className="text-noir-cream/80 mb-6">
            All plans include access to our secure, professional-grade platform with 24/7 availability.
            Start your screenwriting journey immediately.
          </p>
          <p className="text-noir-amber text-sm mb-4">
            Cancel anytime • No hidden fees • 7-day money-back guarantee
          </p>
          {!user && (
            <Button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              disabled={loadingAction || loadingSubscription}
              className={`font-semibold px-8 py-3 ${
                loadingAction || loadingSubscription
                  ? 'bg-noir-charcoal border border-noir-amber/50 text-noir-amber/70 cursor-not-allowed'
                  : 'bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber'
              }`}
            >
              {loadingAction || loadingSubscription ? (
                <div className="flex items-center justify-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                'Choose Your Plan Above'
              )}
            </Button>
          )}
        </div>
      </div>

      <WaitlistModal 
        isOpen={isWaitlistOpen}
        onClose={() => setIsWaitlistOpen(false)}
      />

      <PaygCreditsModal
        isOpen={isPaygModalOpen}
        onClose={() => setIsPaygModalOpen(false)}
        onPurchaseComplete={() => {
          // Optionally refresh subscription data or show success message
          toast({
            title: "Credits purchased!",
            description: "Your additional credits have been added to your account.",
          });
        }}
      />

      {/* Stripe Redirect Loading Overlay */}
      {showStripeRedirect && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-noir-deep-charcoal/95 backdrop-blur-sm">
          <div className="bg-noir-shadow border border-noir-amber/30 rounded-lg p-8 max-w-md mx-4 text-center">
            <div className="flex items-center justify-center mb-6">
              <Loader2 className="w-12 h-12 text-noir-amber animate-spin" />
            </div>
            <h2 className="font-hollywood text-2xl font-semibold text-noir-amber mb-4">
              Redirecting to Payment
            </h2>
            <p className="text-noir-cream/80 mb-2">
              We're securely redirecting you to Stripe to complete your payment.
            </p>
            <p className="text-noir-cream/60 text-sm">
              Please do not close this window...
            </p>
            <div className="mt-6 flex items-center justify-center space-x-1">
              <div className="w-2 h-2 bg-noir-amber rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-noir-amber rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="w-2 h-2 bg-noir-amber rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </div>
        </div>
      )}

      {/* Cancel Subscription Confirmation Dialog */}
      <AlertDialog open={showCancelConfirmation} onOpenChange={setShowCancelConfirmation}>
        <AlertDialogContent className="bg-noir-shadow border border-noir-amber/30 text-noir-cream max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-400" />
              <AlertDialogTitle className="text-noir-amber">
                Cancel Subscription?
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-noir-cream/80">
              Are you sure you want to cancel your subscription? You'll still have access to your current plan benefits until the end of your billing period, but your subscription won't renew.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex space-x-2">
            <AlertDialogCancel 
              className="bg-noir-charcoal border border-noir-amber/30 text-noir-cream hover:bg-noir-amber/10"
              disabled={loadingAction}
            >
              Keep Subscription
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelSubscription}
              disabled={loadingAction}
              className="bg-red-600 text-white hover:bg-red-700 border-0"
            >
              {loadingAction ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Canceling...</span>
                </div>
              ) : (
                'Yes, Cancel Subscription'
              )}
            </AlertDialogAction>
                     </AlertDialogFooter>
         </AlertDialogContent>
       </AlertDialog>

      {/* Upgrade Confirmation Dialog */}
      <AlertDialog open={showUpgradeConfirmation} onOpenChange={setShowUpgradeConfirmation}>
        <AlertDialogContent className="bg-noir-shadow border border-noir-amber/30 text-noir-cream max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-2">
              <Star className="w-5 h-5 text-noir-amber" />
              <AlertDialogTitle className="text-noir-amber">
                Upgrade Subscription?
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-noir-cream/80">
              {pendingTierChange && (
                <>
                  Are you sure you want to upgrade to <span className="text-noir-amber font-semibold">{pendingTierChange.tierName}</span>? 
                  You'll be charged immediately and gain access to all the new features right away.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex space-x-2">
            <AlertDialogCancel 
              className="bg-noir-charcoal border border-noir-amber/30 text-noir-cream hover:bg-noir-amber/10"
              disabled={loadingAction}
            >
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => pendingTierChange && handleUpgrade(pendingTierChange.tierName)}
              disabled={loadingAction}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber border-0"
            >
              {loadingAction ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                'Yes, Upgrade Now'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Downgrade Confirmation Dialog */}
      <AlertDialog open={showDowngradeConfirmation} onOpenChange={setShowDowngradeConfirmation}>
        <AlertDialogContent className="bg-noir-shadow border border-noir-amber/30 text-noir-cream max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-orange-400" />
              <AlertDialogTitle className="text-noir-amber">
                Downgrade Subscription?
              </AlertDialogTitle>
            </div>
            <AlertDialogDescription className="text-noir-cream/80">
              {pendingTierChange && (
                <>
                  Are you sure you want to downgrade to <span className="text-noir-amber font-semibold">{pendingTierChange.tierName}</span>? 
                  You'll lose access to some features, but the change will take effect at your next billing cycle.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex space-x-2">
            <AlertDialogCancel 
              className="bg-noir-charcoal border border-noir-amber/30 text-noir-cream hover:bg-noir-amber/10"
              disabled={loadingAction}
            >
              Keep Current Plan
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => pendingTierChange && handleDowngrade(pendingTierChange.tierName, pendingTierChange.billingCycle)}
              disabled={loadingAction}
              className="bg-orange-600 text-white hover:bg-orange-700 border-0"
            >
              {loadingAction ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                'Yes, Downgrade'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Pricing;
