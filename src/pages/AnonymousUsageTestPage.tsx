import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useUsage } from '@/contexts/UsageContext';
import AnonymousUsageTest from '@/components/AnonymousUsageTest';
import EmailUpgradeUsageTest from '@/components/EmailUpgradeUsageTest';
import AnonymousEdgeCaseTest from '@/components/AnonymousEdgeCaseTest';
import { TestTube, User, MessageCircle, CreditCard, Mail, AlertTriangle } from 'lucide-react';

const AnonymousUsageTestPage: React.FC = () => {
  const { user, isAnonymous } = useAuth();
  const { usageData } = useUsage();

  return (
    <div className="min-h-screen bg-noir-charcoal text-noir-cream p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card className="bg-noir-shadow border-noir-amber/20">
          <CardHeader>
            <CardTitle className="text-2xl font-hollywood text-noir-amber flex items-center">
              <TestTube className="w-6 h-6 mr-3" />
              Anonymous User Usage Integration Tests
            </CardTitle>
            <p className="text-noir-cream/80">
              Comprehensive testing suite for anonymous user integration with the usage tracking system.
              This page tests all aspects of anonymous user functionality including message counting, 
              quota enforcement, PAYG credits, email upgrades, and edge cases.
            </p>
          </CardHeader>
          <CardContent>
            {/* Current User Status */}
            <div className="flex flex-wrap items-center gap-4">
              <Badge variant={user ? "default" : "secondary"} className="flex items-center">
                <User className="w-3 h-3 mr-1" />
                {user ? (isAnonymous ? 'Anonymous User' : 'Registered User') : 'Not Signed In'}
              </Badge>
              
              {user && (
                <Badge variant="outline" className="text-xs">
                  ID: {user.id.substring(0, 8)}...
                </Badge>
              )}

              {usageData && (
                <>
                  <Badge variant="outline" className="flex items-center">
                    <MessageCircle className="w-3 h-3 mr-1" />
                    {usageData.messages_used}/{usageData.messages_limit} messages
                  </Badge>
                  
                  {usageData.payg_credits_remaining > 0 && (
                    <Badge variant="outline" className="flex items-center">
                      <CreditCard className="w-3 h-3 mr-1" />
                      {usageData.payg_credits_remaining} PAYG credits
                    </Badge>
                  )}
                  
                  <Badge variant="outline">
                    Tier: {usageData.tier}
                  </Badge>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Test Instructions */}
        <Card className="bg-noir-burgundy/20 border-noir-amber/30">
          <CardContent className="p-4">
            <h3 className="text-noir-amber font-semibold mb-2 flex items-center">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Test Instructions
            </h3>
            <div className="text-sm text-noir-cream/90 space-y-2">
              <p>
                <strong>To run these tests effectively:</strong>
              </p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>Sign in anonymously first to test anonymous user functionality</li>
                <li>Run the "Basic Usage Tests" to verify core functionality</li>
                <li>Use "Email Upgrade Test" to test data persistence during upgrades</li>
                <li>Run "Edge Case Tests" to verify system robustness</li>
                <li>Check browser console for detailed logging during tests</li>
              </ul>
              <p className="text-noir-amber/80 text-xs mt-3">
                Note: These tests create and clean up test data automatically. Some tests may modify your usage counts temporarily.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Test Tabs */}
        <Tabs defaultValue="basic" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 bg-noir-shadow border border-noir-amber/20">
            <TabsTrigger 
              value="basic" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Basic Usage Tests
            </TabsTrigger>
            <TabsTrigger 
              value="upgrade" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal"
            >
              <Mail className="w-4 h-4 mr-2" />
              Email Upgrade Test
            </TabsTrigger>
            <TabsTrigger 
              value="edge-cases" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal"
            >
              <AlertTriangle className="w-4 h-4 mr-2" />
              Edge Case Tests
            </TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <Card className="bg-noir-shadow border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber">Basic Anonymous Usage Integration</CardTitle>
                <p className="text-noir-cream/80 text-sm">
                  Tests core functionality including anonymous user detection, usage tracking, 
                  message counting, quota enforcement, and PAYG credit integration.
                </p>
              </CardHeader>
              <CardContent>
                <AnonymousUsageTest />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="upgrade" className="space-y-4">
            <Card className="bg-noir-shadow border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber">Email Upgrade Usage Persistence</CardTitle>
                <p className="text-noir-cream/80 text-sm">
                  Tests that usage data (message counts, PAYG credits, subscription info) 
                  persists correctly when an anonymous user upgrades to an email account.
                </p>
              </CardHeader>
              <CardContent>
                <EmailUpgradeUsageTest />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="edge-cases" className="space-y-4">
            <Card className="bg-noir-shadow border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber">Edge Cases & Stress Tests</CardTitle>
                <p className="text-noir-cream/80 text-sm">
                  Tests edge cases including rapid message sending, concurrent usage checks, 
                  database consistency, session persistence, and boundary conditions.
                </p>
              </CardHeader>
              <CardContent>
                <AnonymousEdgeCaseTest />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Requirements Coverage */}
        <Card className="bg-noir-shadow border-noir-amber/20">
          <CardHeader>
            <CardTitle className="text-noir-amber">Requirements Coverage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-semibold text-noir-amber">Task 5 Requirements:</h4>
                <ul className="space-y-1 text-noir-cream/80">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Verify anonymous users work with existing UsageContext
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Test message counting and quota enforcement
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Ensure PAYG credits work through existing system
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Verify usage data persists through email upgrade
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2" />
                    Test edge cases like multiple anonymous sessions
                  </li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-noir-amber">Referenced Requirements:</h4>
                <ul className="space-y-1 text-noir-cream/80 text-xs">
                  <li>2.1 - Anonymous users can send up to 3 messages</li>
                  <li>2.2 - Anonymous users prompted for email after limit</li>
                  <li>5.3 - Anonymous users can purchase PAYG credits</li>
                  <li>5.4 - PAYG credits work with existing billing</li>
                  <li>6.1 - Usage data preserved during email upgrade</li>
                  <li>6.2 - Upgraded users get full subscription limits</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnonymousUsageTestPage;