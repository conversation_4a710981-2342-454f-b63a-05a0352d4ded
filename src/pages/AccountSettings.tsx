import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowLeft, Settings, User, BarChart, Camera, Link, Eye, EyeOff, Mail, Shield, Upload } from 'lucide-react';
import UsageDisplay from '@/components/UsageDisplay';
import Header from '@/components/Header';


interface SubscriptionData {
  subscribed: boolean;
  subscription_tier: string | null;
  subscription_end: string | null;
}

interface ProfileData {
  full_name: string;
  email: string;
  avatar_url: string | null;
}

interface UsageStats {
  totalMessages: number;
  favoriteCharacters: Array<{ name: string; messageCount: number }>;
  memberSince: string;
  currentPeriodUsage: number;
  totalAllowed: number;
}

const AccountSettings = () => {
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const { toast } = useToast();
  
  const [loading, setLoading] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    subscribed: false,
    subscription_tier: null,
    subscription_end: null
  });
  const [profileData, setProfileData] = useState<ProfileData>({
    full_name: '',
    email: '',
    avatar_url: null
  });
  const [usageStats, setUsageStats] = useState<UsageStats>({
    totalMessages: 0,
    favoriteCharacters: [],
    memberSince: '',
    currentPeriodUsage: 0,
    totalAllowed: 20
  });
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isGoogleConnected, setIsGoogleConnected] = useState(false);

  useEffect(() => {
    if (user) {
      loadUserData();
      checkSubscriptionStatus();
      loadUsageStats();
      checkGoogleConnection();
    }
  }, [user]);

  const loadUserData = async () => {
    if (!user) return;
    
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('full_name, email, avatar_url')
        .eq('id', user.id)
        .single();

      if (error) throw error;

      if (profile) {
        let avatarUrl = profile.avatar_url;
        
        // If avatar_url is a file path (not a full URL), create a signed URL
        if (avatarUrl && !avatarUrl.startsWith('http')) {
          const { data: signedUrlData, error: signedUrlError } = await supabase.storage
            .from('hollywood-table')
            .createSignedUrl(avatarUrl, 60 * 60 * 24); // 24 hours expiry for display
          
          if (signedUrlError) {
            console.error('Error creating signed URL for existing avatar:', signedUrlError);
            // Keep the original path if signed URL fails
          } else if (signedUrlData?.signedUrl) {
            avatarUrl = signedUrlData.signedUrl;
          }
        }
        
        setProfileData({
          full_name: profile.full_name || '',
          email: profile.email || user.email || '',
          avatar_url: avatarUrl
        });
      }
    } catch (error) {
      console.error('Error loading profile:', error);
    }
  };

  const checkGoogleConnection = async () => {
    if (!user) return;
    
    try {
      // Check if user has Google provider linked
      const { data: identities } = await supabase.auth.getUserIdentities();
      const hasGoogle = identities?.identities?.some(identity => identity.provider === 'google');
      setIsGoogleConnected(!!hasGoogle);
    } catch (error) {
      console.error('Error checking Google connection:', error);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file.",
        variant: "destructive"
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB.",
        variant: "destructive"
      });
      return;
    }

    setUploadingImage(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `avatar.${fileExt}`;
      const filePath = `avatars/${user.id}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('hollywood-table')
        .upload(filePath, file, {
          upsert: true // This will overwrite if file already exists
        });

      if (uploadError) throw uploadError;

      // For private buckets, we need to create a signed URL
      const { data: signedUrlData, error: signedUrlError } = await supabase.storage
        .from('hollywood-table')
        .createSignedUrl(filePath, 60 * 60 * 24 * 365); // 1 year expiry

      if (signedUrlError) {
        console.error('Signed URL Error:', signedUrlError);
        throw signedUrlError;
      }

      if (!signedUrlData?.signedUrl) {
        throw new Error('Failed to generate signed URL');
      }

      // Update profile with the file path (not the signed URL, as it expires)
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ avatar_url: filePath })
        .eq('id', user.id);

      if (updateError) throw updateError;

      setProfileData(prev => ({ ...prev, avatar_url: signedUrlData.signedUrl }));
      
      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been successfully updated."
      });
    } catch (error: any) {
      console.error('Avatar upload error:', error);
      toast({
        title: "Upload failed",
        description: error.message || "There was an error uploading your profile picture.",
        variant: "destructive"
      });
    } finally {
      setUploadingImage(false);
    }
  };

  const handleConnectGoogle = async () => {
    try {
      const { error } = await supabase.auth.linkIdentity({
        provider: 'google'
      });

      if (error) throw error;

      toast({
        title: "Google connected",
        description: "Your Google account has been successfully connected."
      });
      setIsGoogleConnected(true);
    } catch (error) {
      toast({
        title: "Connection failed",
        description: "There was an error connecting your Google account.",
        variant: "destructive"
      });
    }
  };

  const handleDisconnectGoogle = async () => {
    try {
      const { data: identities } = await supabase.auth.getUserIdentities();
      const googleIdentity = identities?.identities?.find(identity => identity.provider === 'google');
      
      if (googleIdentity) {
        const { error } = await supabase.auth.unlinkIdentity(googleIdentity);
        if (error) throw error;

        toast({
          title: "Google disconnected",
          description: "Your Google account has been disconnected."
        });
        setIsGoogleConnected(false);
      }
    } catch (error) {
      toast({
        title: "Disconnection failed",
        description: "There was an error disconnecting your Google account.",
        variant: "destructive"
      });
    }
  };

  const checkSubscriptionStatus = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('api/billing/subscription', {
        method: 'GET',
      });
      if (error) throw error;
      
      if (data) {
        setSubscriptionData(data);
      }
    } catch (error) {
      console.error('Error checking subscription:', error);
    }
  };

  const loadUsageStats = async () => {
    if (!user) return;

    try {
      // Get total messages
      const { data: messagesData, error: messagesError } = await supabase
        .from('messages')
        .select('*, conversations!inner(user_id, character_name)')
        .eq('conversations.user_id', user.id);

      if (messagesError) throw messagesError;

      // Calculate favorite characters
      const characterCounts: { [key: string]: number } = {};
      messagesData?.forEach(message => {
        const characterName = message.conversations.character_name;
        characterCounts[characterName] = (characterCounts[characterName] || 0) + 1;
      });

      const favoriteCharacters = Object.entries(characterCounts)
        .map(([name, count]) => ({ name, messageCount: count }))
        .sort((a, b) => b.messageCount - a.messageCount)
        .slice(0, 3);

      // Get member since date
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('created_at')
        .eq('id', user.id)
        .single();

      if (profileError) throw profileError;

      // Calculate current period usage (this month)
      const currentMonth = new Date();
      currentMonth.setDate(1);
      currentMonth.setHours(0, 0, 0, 0);

      const { data: currentPeriodMessages, error: periodError } = await supabase
        .from('messages')
        .select('*, conversations!inner(user_id)')
        .eq('conversations.user_id', user.id)
        .gte('created_at', currentMonth.toISOString());

      if (periodError) throw periodError;

      const tierLimits = {
        'Sample the Table': 20,
        'Reserved Seat': 100,
        'VIP Table': 1000
      };

      setUsageStats({
        totalMessages: messagesData?.length || 0,
        favoriteCharacters,
        memberSince: profileData?.created_at || '',
        currentPeriodUsage: currentPeriodMessages?.length || 0,
        totalAllowed: tierLimits[subscriptionData.subscription_tier as keyof typeof tierLimits] || 20
      });
    } catch (error) {
      console.error('Error loading usage stats:', error);
    }
  };

  const handleProfileUpdate = async () => {
    if (!user) return;
    
    setLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: profileData.full_name,
          email: profileData.email
        })
        .eq('id', user.id);

      if (error) throw error;

      toast({
        title: "Profile updated",
        description: "Your profile information has been successfully updated."
      });
    } catch (error) {
      toast({
        title: "Update failed",
        description: "There was an error updating your profile.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Password mismatch",
        description: "New passwords do not match.",
        variant: "destructive"
      });
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      toast({
        title: "Password too short",
        description: "Password must be at least 8 characters long.",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: passwordForm.newPassword
      });

      if (error) throw error;

      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      toast({
        title: "Password updated",
        description: "Your password has been successfully changed."
      });
    } catch (error) {
      toast({
        title: "Password change failed",
        description: "There was an error changing your password.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleManageBilling = async () => {
    try {
      const { data, error } = await supabase.functions.invoke('api/billing/customer-portal', {
        method: 'POST',
      });
      if (error) throw error;
      
      if (data?.url) {
        window.open(data.url, '_blank');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Unable to access billing portal. Please try again.",
        variant: "destructive"
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className="min-h-screen bg-noir-black text-noir-cream">
      <Header />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header with Breadcrumb */}
        <div className="mb-8">


          

          <h1 className="font-hollywood text-4xl font-bold text-noir-amber mb-2">
            Account Settings
          </h1>
          <p className="text-noir-cream/70 text-lg font-source-sans">
            Manage your profile, billing, and usage preferences
          </p>
        </div>

        <Tabs defaultValue="usage" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-noir-charcoal/95 backdrop-blur-sm border border-noir-amber/20">
            <TabsTrigger 
              value="usage" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal font-source-sans transition-all duration-300 text-noir-cream hover:bg-noir-amber/20"
            >
              <BarChart className="w-4 h-4 mr-2" />
              Usage
            </TabsTrigger>
            <TabsTrigger 
              value="billing" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal font-source-sans transition-all duration-300 text-noir-cream hover:bg-noir-amber/20"
            >
              <Settings className="w-4 h-4 mr-2" />
              Billing
            </TabsTrigger>
            <TabsTrigger 
              value="profile" 
              className="data-[state=active]:bg-noir-amber data-[state=active]:text-noir-charcoal font-source-sans transition-all duration-300 text-noir-cream hover:bg-noir-amber/20"
            >
              <User className="w-4 h-4 mr-2" />
              Profile
            </TabsTrigger>
          </TabsList>

          {/* Usage Tab - Now Default */}
          <TabsContent value="usage" className="space-y-6">
            <UsageDisplay />
            
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber font-source-sans">Activity Statistics</CardTitle>
                <CardDescription className="text-noir-cream/70 font-source-sans">
                  Your conversation history and favorite characters
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-noir-shadow p-4 rounded-lg border border-noir-amber/20">
                    <h3 className="font-semibold text-noir-amber font-source-sans">Total Messages</h3>
                    <p className="text-2xl font-bold text-noir-cream font-source-sans">{usageStats.totalMessages}</p>
                    <p className="text-sm text-noir-cream/70 font-source-sans">all time</p>
                  </div>
                  <div className="bg-noir-shadow p-4 rounded-lg border border-noir-amber/20">
                    <h3 className="font-semibold text-noir-amber font-source-sans">Member Since</h3>
                    <p className="text-lg font-bold text-noir-cream font-source-sans">
                      {usageStats.memberSince ? formatDate(usageStats.memberSince) : 'Recently'}
                    </p>
                  </div>
                </div>

                {usageStats.favoriteCharacters.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-noir-amber font-source-sans">Favorite Characters</h3>
                    <div className="space-y-2">
                      {usageStats.favoriteCharacters.map((character, index) => (
                        <div key={character.name} className="flex items-center justify-between p-3 bg-noir-shadow rounded-lg border border-noir-amber/20">
                          <div className="flex items-center gap-3">
                            <Badge variant="outline" className="border-noir-amber/30 text-noir-amber font-source-sans">
                              #{index + 1}
                            </Badge>
                            <span className="font-medium text-noir-cream font-source-sans">{character.name}</span>
                          </div>
                          <span className="text-noir-cream/70 font-source-sans">{character.messageCount} messages</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing" className="space-y-6">
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber font-source-sans">Billing Information</CardTitle>
                <CardDescription className="text-noir-cream/70 font-source-sans">
                  Manage your payment methods and billing history
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-noir-shadow rounded-lg border border-noir-amber/20">
                  <div>
                    <h3 className="font-semibold text-noir-cream font-source-sans">Payment Method</h3>
                    <p className="text-sm text-noir-cream/70 font-source-sans">
                      {subscriptionData.subscribed ? 'Card ending in ••••' : 'No payment method on file'}
                    </p>
                  </div>
                  <Button 
                    onClick={handleManageBilling}
                    className="bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button font-source-sans"
                  >
                    Manage Billing
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-noir-shadow rounded-lg border border-noir-amber/20">
                    <div>
                      <h3 className="font-semibold text-noir-cream font-source-sans">Current Plan</h3>
                      <p className="text-sm text-noir-cream/70 font-source-sans">
                        {subscriptionData.subscription_tier || 'Sample the Table'}
                      </p>
                    </div>
                    <Button 
                      onClick={() => navigate('/pricing')}
                      className="bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300 font-source-sans"
                    >
                      Manage Plan
                    </Button>
                  </div>
                </div>

                {subscriptionData.subscribed && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-noir-amber font-source-sans">Billing History</h3>
                    <div className="bg-noir-shadow rounded-lg border border-noir-amber/20 p-4">
                      <p className="text-noir-cream/70 font-source-sans">
                        Access your complete billing history and download receipts through the billing portal.
                      </p>
                      <Button 
                        onClick={handleManageBilling}
                        className="mt-4 bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300 font-source-sans"
                      >
                        View Billing History
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Profile Tab - Completely Redesigned */}
          <TabsContent value="profile" className="space-y-6">
            {/* Profile Header Card */}
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardContent className="pt-6">
                <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                  {/* Profile Picture Section */}
                  <div className="relative group">
                    <Avatar className="w-24 h-24 border-2 border-noir-amber/30">
                      <AvatarImage src={profileData.avatar_url || ''} alt="Profile picture" />
                      <AvatarFallback className="bg-noir-shadow text-noir-amber text-xl font-source-sans">
                        {profileData.full_name ? getInitials(profileData.full_name) : 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute inset-0 bg-noir-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full flex items-center justify-center cursor-pointer">
                      <Camera className="w-6 h-6 text-noir-amber" />
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                      disabled={uploadingImage}
                    />
                  </div>

                  {/* Profile Info */}
                  <div className="flex-1 space-y-2">
                    <h2 className="text-2xl font-bold text-noir-amber font-source-sans">
                      {profileData.full_name || 'Your Name'}
                    </h2>
                    <p className="text-noir-cream/70 font-source-sans flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      {profileData.email}
                    </p>
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4 text-noir-amber" />
                      <span className="text-sm text-noir-cream/70 font-source-sans">
                        Member since {usageStats.memberSince ? formatDate(usageStats.memberSince) : 'Recently'}
                      </span>
                    </div>
                  </div>

                  {/* Upload Button */}
                  <div className="relative">
                    <Button
                      disabled={uploadingImage}
                      className="bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button font-source-sans"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      {uploadingImage ? 'Uploading...' : 'Change Photo'}
                    </Button>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                      disabled={uploadingImage}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Personal Information */}
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber font-source-sans flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Personal Information
                </CardTitle>
                <CardDescription className="text-noir-cream/70 font-source-sans">
                  Update your personal details and contact information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="fullName" className="text-noir-cream font-source-sans text-sm font-medium">
                      Full Name
                    </Label>
                    <Input
                      id="fullName"
                      value={profileData.full_name}
                      onChange={(e) => setProfileData(prev => ({ ...prev, full_name: e.target.value }))}
                      className="font-source-sans"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-noir-cream font-source-sans text-sm font-medium">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={profileData.email}
                      onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                      className="font-source-sans"
                      placeholder="Enter your email address"
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button 
                    onClick={handleProfileUpdate}
                    disabled={loading}
                    className="bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300 font-source-sans"
                  >
                    {loading ? 'Saving...' : 'Save Changes'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Connected Accounts */}
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber font-source-sans flex items-center gap-2">
                  <Link className="w-5 h-5" />
                  Connected Accounts
                </CardTitle>
                <CardDescription className="text-noir-cream/70 font-source-sans">
                  Manage your connected social accounts for easy sign-in
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between p-4 bg-noir-shadow rounded-lg border border-noir-amber/20">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-semibold text-noir-cream font-source-sans">Google</h3>
                      <p className="text-sm text-noir-cream/70 font-source-sans">
                        {isGoogleConnected ? 'Connected' : 'Not connected'}
                      </p>
                    </div>
                  </div>
                  {isGoogleConnected ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="outline"
                          className="bg-noir-shadow border border-red-500/50 text-red-400 hover:bg-red-500/20 hover:text-red-300 hover:border-red-400 transition-all duration-300 font-source-sans"
                        >
                          Disconnect
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="bg-noir-charcoal border-noir-amber/30">
                        <AlertDialogHeader>
                          <AlertDialogTitle className="text-noir-amber font-source-sans">Disconnect Google Account</AlertDialogTitle>
                          <AlertDialogDescription className="text-noir-cream/70 font-source-sans">
                            Are you sure you want to disconnect your Google account? You'll need to use your email and password to sign in.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel className="bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 font-source-sans">
                            Cancel
                          </AlertDialogCancel>
                          <AlertDialogAction 
                            onClick={handleDisconnectGoogle}
                            className="bg-red-600 hover:bg-red-700 text-white font-source-sans"
                          >
                            Disconnect
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <Button 
                      onClick={handleConnectGoogle}
                      className="bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300 font-source-sans"
                    >
                      Connect
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Security Settings */}
            <Card className="bg-noir-charcoal/95 backdrop-blur-sm border-noir-amber/20">
              <CardHeader>
                <CardTitle className="text-noir-amber font-source-sans flex items-center gap-2">
                  <Shield className="w-5 h-5" />
                  Security Settings
                </CardTitle>
                <CardDescription className="text-noir-cream/70 font-source-sans">
                  Update your password and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="currentPassword" className="text-noir-cream font-source-sans text-sm font-medium">
                        Current Password
                      </Label>
                      <div className="relative">
                        <Input
                          id="currentPassword"
                          type={showCurrentPassword ? "text" : "password"}
                          value={passwordForm.currentPassword}
                          onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                          className="font-source-sans pr-10"
                          placeholder="Enter current password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? (
                            <EyeOff className="h-4 w-4 text-noir-cream/50" />
                          ) : (
                            <Eye className="h-4 w-4 text-noir-cream/50" />
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newPassword" className="text-noir-cream font-source-sans text-sm font-medium">
                        New Password
                      </Label>
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={showNewPassword ? "text" : "password"}
                          value={passwordForm.newPassword}
                          onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                          className="font-source-sans pr-10"
                          placeholder="Enter new password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? (
                            <EyeOff className="h-4 w-4 text-noir-cream/50" />
                          ) : (
                            <Eye className="h-4 w-4 text-noir-cream/50" />
                          )}
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword" className="text-noir-cream font-source-sans text-sm font-medium">
                        Confirm Password
                      </Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          value={passwordForm.confirmPassword}
                          onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                          className="font-source-sans pr-10"
                          placeholder="Confirm new password"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4 text-noir-cream/50" />
                          ) : (
                            <Eye className="h-4 w-4 text-noir-cream/50" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-sm text-noir-cream/70 font-source-sans">
                    Password must be at least 8 characters long and contain a mix of letters, numbers, and special characters.
                  </div>

                  <div className="flex justify-end">
                    <Button 
                      onClick={handlePasswordChange}
                      disabled={loading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                      className="bg-noir-shadow border border-noir-amber text-noir-amber hover:bg-noir-amber hover:text-noir-charcoal art-deco-button transition-all duration-300 font-source-sans"
                    >
                      {loading ? 'Changing...' : 'Change Password'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AccountSettings;

