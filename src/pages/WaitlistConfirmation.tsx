import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';

const WaitlistConfirmation = () => {
  return (
    <div className="min-h-screen bg-noir-gradient flex items-center justify-center px-6">
      <div className="max-w-2xl mx-auto text-center">
        {/* Success Icon */}
        <div className="flex justify-center mb-8">
          <div className="w-24 h-24 bg-noir-amber rounded-full flex items-center justify-center">
            <CheckCircle className="w-12 h-12 text-noir-charcoal" />
          </div>
        </div>

        {/* Main Heading */}
        <h1 className="font-hollywood text-4xl md:text-5xl text-noir-amber mb-6">
          You're On The List!
        </h1>

        {/* Subheading */}
        <p className="text-xl text-noir-cream/90 mb-8 max-w-xl mx-auto">
          Thank you for confirming your interest in Hollywood Table's beta program.
        </p>

        {/* Status Card */}
        <div className="bg-noir-charcoal/60 backdrop-blur-sm border border-noir-amber/30 rounded-lg p-8 mb-8">
          <div className="flex items-center justify-center mb-4">
            <Clock className="w-6 h-6 text-noir-amber mr-3" />
            <h2 className="font-cinzel text-xl text-noir-amber">
              Beta Access Status
            </h2>
          </div>
          
          <p className="text-noir-cream/80 mb-6">
            We acknowledge your interest in joining our community of storytellers and screenwriting enthusiasts. 
            You will be notified whenever you are picked for the beta program.
          </p>

          <div className="bg-noir-shadow/50 rounded-lg border border-noir-amber/20 p-6">
            <h3 className="text-noir-amber text-lg font-medium mb-4">What happens next?</h3>
            <ul className="text-noir-cream/70 space-y-2 text-left">
              <li className="flex items-start">
                <span className="text-noir-amber mr-2">•</span>
                Our team is carefully selecting beta testers for the best experience
              </li>
              <li className="flex items-start">
                <span className="text-noir-amber mr-2">•</span>
                You'll receive an email with your exclusive invite code when ready
              </li>
              <li className="flex items-start">
                <span className="text-noir-amber mr-2">•</span>
                Get ready for intimate conversations with legendary screenwriters
              </li>
              <li className="flex items-start">
                <span className="text-noir-amber mr-2">•</span>
                Access exclusive storytelling insights and our noir-inspired community
              </li>
            </ul>
          </div>
        </div>

        {/* Call to Action */}
        <div className="space-y-4">
          <Link to="/">
            <Button className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold px-8 py-3">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Return to Hollywood Table
            </Button>
          </Link>
          
          <p className="text-sm text-noir-cream/60">
            Keep an eye on your inbox for updates about your beta invitation.
          </p>
        </div>

        {/* Footer Note */}
        <div className="mt-12 pt-8 border-t border-noir-amber/20">
          <p className="text-xs text-noir-cream/50">
            Questions? Feel free to reach out to us at{' '}
            <a href="mailto:<EMAIL>" className="text-noir-amber hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default WaitlistConfirmation; 