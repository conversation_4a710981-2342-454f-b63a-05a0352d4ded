export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      characters: {
        Row: {
          avatar: string
          description: string
          id: string
          inspiration: string
          name: string
          signature: string
          system_prompt: string | null
          title: string
        }
        Insert: {
          avatar: string
          description: string
          id: string
          inspiration: string
          name: string
          signature: string
          system_prompt?: string | null
          title: string
        }
        Update: {
          avatar?: string
          description?: string
          id?: string
          inspiration?: string
          name?: string
          signature?: string
          system_prompt?: string | null
          title?: string
        }
        Relationships: []
      }
      conversations: {
        Row: {
          character_id: string | null
          character_name: string
          created_at: string
          id: string
          last_message_preview: string | null
          message_count: number | null
          title: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          character_id?: string | null
          character_name: string
          created_at?: string
          id?: string
          last_message_preview?: string | null
          message_count?: number | null
          title?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          character_id?: string | null
          character_name?: string
          created_at?: string
          id?: string
          last_message_preview?: string | null
          message_count?: number | null
          title?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversations_character_id_fkey"
            columns: ["character_id"]
            isOneToOne: false
            referencedRelation: "characters"
            referencedColumns: ["id"]
          },
        ]
      }
      invite_codes: {
        Row: {
          code: string
          created_at: string | null
          expires_at: string | null
          from_user: string | null
          id: string
          used: boolean | null
          used_at: string | null
          used_by: string | null
        }
        Insert: {
          code: string
          created_at?: string | null
          expires_at?: string | null
          from_user?: string | null
          id?: string
          used?: boolean | null
          used_at?: string | null
          used_by?: string | null
        }
        Update: {
          code?: string
          created_at?: string | null
          expires_at?: string | null
          from_user?: string | null
          id?: string
          used?: boolean | null
          used_at?: string | null
          used_by?: string | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string
          conversation_id: string
          created_at: string
          id: string
          sender_type: string
        }
        Insert: {
          content: string
          conversation_id: string
          created_at?: string
          id?: string
          sender_type: string
        }
        Update: {
          content?: string
          conversation_id?: string
          created_at?: string
          id?: string
          sender_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      payg_messages: {
        Row: {
          created_at: string
          id: string
          message_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          message_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          message_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "payg_messages_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: true
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      payg_purchase_history: {
        Row: {
          created_at: string
          credits_purchased: number
          id: string
          price_paid_cents: number
          purchase_date: string
          stripe_payment_intent_id: string | null
          subscription_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          credits_purchased: number
          id?: string
          price_paid_cents: number
          purchase_date?: string
          stripe_payment_intent_id?: string | null
          subscription_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          credits_purchased?: number
          id?: string
          price_paid_cents?: number
          purchase_date?: string
          stripe_payment_intent_id?: string | null
          subscription_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "payg_purchase_history_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      processed_webhook_events: {
        Row: {
          created_at: string
          event_type: string
          id: string
          processed_at: string
          stripe_event_id: string
        }
        Insert: {
          created_at?: string
          event_type: string
          id?: string
          processed_at?: string
          stripe_event_id: string
        }
        Update: {
          created_at?: string
          event_type?: string
          id?: string
          processed_at?: string
          stripe_event_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      user_subscriptions: {
        Row: {
          billing_cycle: string | null
          created_at: string
          current_period_end: string
          current_period_start: string
          id: string
          messages_used_this_period: number
          monthly_message_limit: number
          payg_credits_purchased: number
          payg_credits_remaining: number
          payg_credits_used: number
          payg_total_spent_cents: number
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string
          subscription_tier: string
          updated_at: string
          user_id: string
        }
        Insert: {
          billing_cycle?: string | null
          created_at?: string
          current_period_end?: string
          current_period_start?: string
          id?: string
          messages_used_this_period?: number
          monthly_message_limit?: number
          payg_credits_purchased?: number
          payg_credits_remaining?: number
          payg_credits_used?: number
          payg_total_spent_cents?: number
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          billing_cycle?: string | null
          created_at?: string
          current_period_end?: string
          current_period_start?: string
          id?: string
          messages_used_this_period?: number
          monthly_message_limit?: number
          payg_credits_purchased?: number
          payg_credits_remaining?: number
          payg_credits_used?: number
          payg_total_spent_cents?: number
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string
          subscription_tier?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      waitlist: {
        Row: {
          created_at: string | null
          email: string
          id: string
          name: string
          notified: boolean | null
          reason: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          name: string
          notified?: boolean | null
          reason?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          name?: string
          notified?: boolean | null
          reason?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      user_subscription_overview: {
        Row: {
          current_period_end: string | null
          current_period_start: string | null
          email: string | null
          full_name: string | null
          message_limit_exceeded: boolean | null
          message_usage_percent: number | null
          messages_used_this_period: number | null
          monthly_message_limit: number | null
          payg_credits_remaining: number | null
          payg_total_spent_cents: number | null
          subscription_status: string | null
          subscription_tier: string | null
          user_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_payg_credits: {
        Args: {
          user_uuid: string
          credits_amount: number
          price_paid_cents: number
          stripe_payment_intent_id?: string
        }
        Returns: string
      }
      backfill_payg_messages: {
        Args: Record<PropertyKey, never>
        Returns: {
          processed_users: number
          processed_messages: number
          payg_messages_created: number
        }[]
      }
      can_user_perform_action: {
        Args: { user_uuid: string; action_type: string }
        Returns: {
          can_perform: boolean
          reason: string
          using_payg: boolean
          subscription_info: Json
        }[]
      }
      cancel_subscription_to_free_tier: {
        Args: { user_uuid: string }
        Returns: {
          success: boolean
          message: string
          previous_tier: string
          new_tier: string
          usage_kept: boolean
        }[]
      }
      cleanup_old_processed_webhook_events: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_orphaned_payg_messages: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      consume_payg_credit: {
        Args: { user_uuid: string; action_type: string }
        Returns: {
          success: boolean
          credits_remaining: number
          error_message: string
        }[]
      }
      recalculate_payg_credits_used: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      refresh_user_usage: {
        Args: { user_uuid: string }
        Returns: undefined
      }
      reset_monthly_usage: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      set_billing_cycle: {
        Args: { user_uuid: string; cycle: string }
        Returns: boolean
      }
      update_subscription_tier_limits: {
        Args: { user_uuid: string; new_tier: string }
        Returns: undefined
      }
      use_invite_code: {
        Args: { invite_code: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
