// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://krqhkiwbmudufqyiurbl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImtycWhraXdibXVkdWZxeWl1cmJsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAxNDkzMDIsImV4cCI6MjA2NTcyNTMwMn0.vAXxI1WvtNO6LehfWXp6G_r57WdtDsgk3JygcMZnKeU";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);