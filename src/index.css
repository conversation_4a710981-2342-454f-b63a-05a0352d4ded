
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */
/* Always show scrollbar to prevent layout shift */
html {
  overflow-y: scroll;
  scrollbar-gutter: stable;
}

html body[data-scroll-locked] {
  --removed-body-scroll-bar-size: 0 !important;
  margin-right: 0 !important;
} 

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-noir-deep-charcoal text-noir-cream font-poiret font-medium;
    background-image: radial-gradient(circle at 50% 50%, rgba(212, 175, 55, 0.05) 0%, rgba(15, 15, 15, 1) 100%);
  }
}

@layer components {
  .hollywood-gradient {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2c2c2c 100%);
  }
  
  .amber-glow {
    text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
  }
  
  .booth-leather {
    background: linear-gradient(145deg, #8b1538 0%, #6d1027 50%, #4a0c1c 100%);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .vintage-border {
    border: 2px solid #d4af37;
    border-image: linear-gradient(45deg, #d4af37, #f4d03f, #d4af37) 1;
  }

  /* Art Deco Background Patterns */
  .art-deco-bg {
    position: relative;
  }
  
  .art-deco-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.12;
    background-image: 
      linear-gradient(45deg, transparent 40%, rgba(212, 175, 55, 0.3) 42%, rgba(212, 175, 55, 0.3) 44%, transparent 46%),
      linear-gradient(-45deg, transparent 40%, rgba(212, 175, 55, 0.2) 42%, rgba(212, 175, 55, 0.2) 44%, transparent 46%),
      linear-gradient(90deg, transparent 48%, rgba(212, 175, 55, 0.1) 49%, rgba(212, 175, 55, 0.1) 51%, transparent 52%);
    background-size: 60px 60px, 60px 60px, 120px 120px;
    pointer-events: none;
    z-index: 0;
  }

  /* Art Deco Section Divider */
  .art-deco-divider {
    position: relative;
    height: 2px;
    background: linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.3) 20%, rgba(212, 175, 55, 0.8) 50%, rgba(212, 175, 55, 0.3) 80%, transparent 100%);
    margin: 3rem 0;
  }
  
  .art-deco-divider::before {
    content: '';
    position: absolute;
    left: 50%;
    top: -8px;
    transform: translateX(-50%);
    width: 20px;
    height: 20px;
    background: #d4af37;
    clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
    rotate: 45deg;
  }

  /* Character Card Art Deco Elements */
  .character-card-deco {
    position: relative;
  }
  
  .character-card-deco::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 8px;
    right: 8px;
    bottom: 8px;
    border: 1px solid rgba(212, 175, 55, 0.2);
    border-radius: 0.5rem;
    clip-path: polygon(
      0 0, 20px 0, 20px 2px, 2px 2px, 2px 20px, 0 20px,
      0 100%, 20px 100%, 20px calc(100% - 2px), 2px calc(100% - 2px), 2px calc(100% - 20px), 0 calc(100% - 20px),
      100% 100%, 100% calc(100% - 20px), calc(100% - 2px) calc(100% - 20px), calc(100% - 2px) calc(100% - 2px), calc(100% - 20px) calc(100% - 2px), calc(100% - 20px) 100%,
      100% 0, calc(100% - 20px) 0, calc(100% - 20px) 2px, calc(100% - 2px) 2px, calc(100% - 2px) 20px, 100% 20px
    );
    pointer-events: none;
    z-index: 1;
  }

  /* Character Avatar Art Deco Frame */
  .character-avatar-frame {
    position: relative;
  }
  
  .character-avatar-frame::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid rgba(212, 175, 55, 0.4);
    border-radius: 50%;
    background: conic-gradient(from 0deg, transparent 0deg, rgba(212, 175, 55, 0.2) 45deg, transparent 90deg, rgba(212, 175, 55, 0.2) 135deg, transparent 180deg, rgba(212, 175, 55, 0.2) 225deg, transparent 270deg, rgba(212, 175, 55, 0.2) 315deg, transparent 360deg);
    animation: rotate-frame 8s linear infinite;
  }

  /* Art Deco Button */
  .art-deco-button {
    position: relative;
    overflow: hidden;
  }
  
  .art-deco-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.3) 50%, transparent 100%);
    transition: left 0.5s ease;
    z-index: 0;
  }
  
  .art-deco-button:hover::before {
    left: 100%;
  }
  
  .art-deco-button > * {
    position: relative;
    z-index: 1;
  }

  /* Header Art Deco Pattern */
  .header-deco {
    position: relative;
  }
  
  .header-deco::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.08;
    background-image: 
      linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.4) 1%, transparent 2%),
      linear-gradient(90deg, transparent 98%, rgba(212, 175, 55, 0.4) 99%, transparent 100%);
    background-size: 100px 100%, 100px 100%;
    pointer-events: none;
  }

  /* Navigation Separator */
  .nav-separator {
    width: 1px;
    height: 20px;
    background: linear-gradient(to bottom, transparent 0%, rgba(212, 175, 55, 0.6) 50%, transparent 100%);
    margin: 0 0.5rem;
  }

  /* Art Deco Content Overlay */
  .content-overlay {
    position: relative;
  }
  
  .content-overlay > * {
    position: relative;
    z-index: 2;
  }

  /* Features Section Art Deco */
  .features-deco {
    position: relative;
  }
  
  .features-deco::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.1;
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(212, 175, 55, 0.4) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(212, 175, 55, 0.3) 1px, transparent 1px);
    background-size: 40px 40px, 40px 40px;
    background-position: 0 0, 20px 20px;
    pointer-events: none;
  }

  @keyframes rotate-frame {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
