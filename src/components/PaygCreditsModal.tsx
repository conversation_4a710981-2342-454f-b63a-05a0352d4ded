import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check, CreditCard, Zap, Star, AlertTriangle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { PAYG_PACKAGES_DISPLAY, PaygPackageDisplay } from '@/model/pricing';


interface PaygCreditsModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentCredits?: number;
  onPurchaseComplete?: () => void;
}


const PaygCreditsModal: React.FC<PaygCreditsModalProps> = ({
  isOpen,
  onClose,
  currentCredits = 0,
  onPurchaseComplete
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [selectedPackage, setSelectedPackage] = useState<string>('writers');
  const [isProcessing, setIsProcessing] = useState(false);

  // Use frontend pricing display configuration
  const packages: PaygPackageDisplay[] = PAYG_PACKAGES_DISPLAY;

  const handlePurchase = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to purchase credits.",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      console.log('🛒 Purchasing PAYG credits:', {
        userId: user.id,
        isAnonymous: user.user_metadata?.is_anonymous,
        packageType: selectedPackage,
        hasEmail: !!user.email
      });

      const { data, error } = await supabase.functions.invoke('api/billing/purchase-credits', {
        method: 'POST',
        body: {
          package_type: selectedPackage
        }
      });

      if (error) throw error;

      if (data.checkout_url) {
        toast({
          title: "Redirecting to payment",
          description: user.user_metadata?.is_anonymous 
            ? "Redirecting you to Stripe. After purchase, you can continue messaging immediately!"
            : "Redirecting you to Stripe to complete your purchase...",
        });
        
        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Error purchasing credits:', error);
      
      // Provide specific error handling for anonymous users
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      if (errorMessage.includes('email') && user.user_metadata?.is_anonymous) {
        toast({
          title: "Purchase failed",
          description: "Anonymous users can purchase credits, but you may need to provide an email during checkout for receipts.",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Purchase failed",
          description: "There was an error processing your purchase. Please try again.",
          variant: "destructive"
        });
      }
      setIsProcessing(false);
    }
  };

  const selectedPkg = packages.find(p => p.id === selectedPackage);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-noir-deep-charcoal border-noir-amber/30 text-noir-cream max-w-2xl">
        <DialogHeader>
          <DialogTitle className="font-hollywood text-2xl text-noir-amber flex items-center">
            <Zap className="w-6 h-6 mr-2" />
            Buy Additional Credits
          </DialogTitle>
          <DialogDescription className="text-noir-cream/80">
            Purchase pay-as-you-go credits to continue messaging beyond your plan limits.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Credits Display */}
          <div className="bg-noir-shadow border border-noir-amber/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CreditCard className="w-5 h-5 text-noir-amber mr-2" />
                <span className="text-noir-cream/80">Current Credits:</span>
              </div>
              <Badge variant="outline" className="border-noir-amber text-noir-amber">
                {currentCredits} credits
              </Badge>
            </div>
          </div>

          {/* Package Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-noir-amber">Choose Your Credit Package:</h3>
            <div className="grid gap-4">
              {packages.map((pkg) => (
                <Card
                  key={pkg.id}
                  className={`relative cursor-pointer transition-all duration-200 ${
                    selectedPackage === pkg.id
                      ? 'bg-noir-amber/10 border-noir-amber ring-2 ring-noir-amber/50'
                      : 'bg-noir-shadow border-noir-amber/20 hover:border-noir-amber/50'
                  }`}
                  onClick={() => setSelectedPackage(pkg.id)}
                >
                  {'popular' in pkg && pkg.popular && (
                    <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                      <Badge variant="outline" className="bg-noir-amber text-noir-charcoal border-noir-amber hover:bg-noir-amber hover:text-noir-charcoal">
                        <Star className="w-3 h-3 mr-1" />
                        {'savings' in pkg ? pkg.savings : 'Popular'}
                      </Badge>
                    </div>
                  )}
                  
                  <div className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          selectedPackage === pkg.id
                            ? 'bg-noir-amber border-noir-amber'
                            : 'border-noir-amber/50'
                        }`} />
                        <div>
                          <h4 className="font-semibold text-noir-amber">{pkg.name}</h4>
                          <p className="text-sm text-noir-cream/70">{pkg.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-noir-cream">${pkg.price_dollars}</div>
                        <div className="text-sm text-noir-amber">{pkg.credits} credits</div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Purchase Summary */}
          {selectedPkg && (
            <div className="bg-noir-burgundy/20 border border-noir-amber/30 rounded-lg p-4">
              <h4 className="font-semibold text-noir-amber mb-2">Purchase Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-noir-cream/80">Package:</span>
                  <span className="text-noir-cream">{selectedPkg.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-noir-cream/80">Credits:</span>
                  <span className="text-noir-cream">{selectedPkg.credits}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-noir-cream/80">Price:</span>
                  <span className="text-noir-cream">${selectedPkg.price_dollars}</span>
                </div>
                <div className="border-t border-noir-amber/20 pt-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-noir-amber">Total:</span>
                    <span className="text-noir-amber">${selectedPkg.price_dollars}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              onClick={onClose}
              variant="outline"
              className="flex-1 bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button"
            >
              Cancel
            </Button>
            <Button
              onClick={handlePurchase}
              disabled={isProcessing}
              className="flex-1 bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-noir-charcoal mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4 mr-2" />
                  Purchase Credits
                </>
              )}
            </Button>
          </div>

          {/* Anonymous User Notice */}
          {user?.user_metadata?.is_anonymous && (
            <div className="bg-noir-burgundy/20 border border-noir-amber/30 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-noir-amber mt-0.5 flex-shrink-0" />
                <div className="text-sm text-noir-cream/90">
                  <p className="font-medium text-noir-amber mb-1">Anonymous User Purchase</p>
                  <p>
                    You can purchase credits as an anonymous user. After purchase, you'll be able to continue 
                    messaging immediately. Consider providing an email during checkout for purchase receipts.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Terms */}
          <div className="text-xs text-noir-cream/60 text-center">
            <p>
              Credits are non-refundable and don't expire. 
              By purchasing, you agree to our Terms of Service.
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PaygCreditsModal; 