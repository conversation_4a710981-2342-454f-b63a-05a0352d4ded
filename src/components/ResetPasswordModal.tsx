import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2 } from 'lucide-react';

interface ResetPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  email?: string;
}

const ResetPasswordModal = ({ isOpen, onClose, email }: ResetPasswordModalProps) => {
  const [step, setStep] = useState<'request' | 'reset'>('request');
  const [emailInput, setEmailInput] = useState(email || '');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { resetPassword } = useAuth();

  useEffect(() => {
    // Check if we have the required tokens for password reset
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');
    
    if (accessToken && refreshToken && isOpen) {
      setStep('reset');
    } else if (isOpen) {
      setStep('request');
    }
  }, [searchParams, isOpen]);

  useEffect(() => {
    if (email) {
      setEmailInput(email);
    }
  }, [email]);

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!emailInput.trim()) {
      toast({
        title: "Email required",
        description: "Please enter your email address.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const { error } = await resetPassword(emailInput);

      if (error) {
        toast({
          title: "Reset request failed",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Reset link sent",
          description: "Check your email for a password reset link.",
        });
        onClose();
      }
    } catch (error) {
      console.error('Password reset request error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "Please make sure both passwords are the same.",
        variant: "destructive",
      });
      return;
    }

    if (password.length < 6) {
      toast({
        title: "Password too short",
        description: "Password must be at least 6 characters long.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        toast({
          title: "Password update failed",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Password updated",
          description: "Your password has been successfully updated.",
        });
        onClose();
      }
    } catch (error) {
      console.error('Password reset error:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setEmailInput(email || '');
    setPassword('');
    setConfirmPassword('');
    setStep('request');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-md">
        <DialogHeader>
          <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
            {step === 'request' ? 'Reset Password' : 'Set New Password'}
          </DialogTitle>
          <DialogDescription className="text-noir-cream/70 text-center">
            {step === 'request' 
              ? 'Enter your email to receive a password reset link'
              : 'Enter your new password below'
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          {step === 'request' ? (
            <form onSubmit={handleRequestReset} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-noir-cream/90">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={emailInput}
                  onChange={(e) => setEmailInput(e.target.value)}
                  className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              <Button
                type="submit"
                className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
                disabled={loading}
              >
                {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Send Reset Link
              </Button>
            </form>
          ) : (
            <form onSubmit={handlePasswordReset} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="password" className="text-noir-cream/90">New Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                  placeholder="Enter your new password"
                  required
                  minLength={6}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-noir-cream/90">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                  placeholder="Confirm your new password"
                  required
                  minLength={6}
                />
              </div>

              <Button
                type="submit"
                disabled={loading}
                className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
              >
                {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                Update Password
              </Button>
            </form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResetPasswordModal; 