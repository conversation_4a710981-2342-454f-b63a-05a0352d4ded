import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import WaitlistModal from '@/components/WaitlistModal';
import type { Character } from '@/hooks/useCharacters';

interface CharacterCardProps {
  character: Character;
  isSelected?: boolean;
}

const CharacterCard = ({ character, isSelected = false }: CharacterCardProps) => {
  const { user, signInAnonymously } = useAuth();
  const navigate = useNavigate();
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);
  const [isSigningIn, setIsSigningIn] = useState(false);

  const handleAbout = (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('About button clicked for:', character.name);
    navigate(`/about/${character.id}`);
  };

  const handleCardClick = () => {
    console.log('Card clicked for:', character.name);
    navigate(`/about/${character.id}`);
  };

  const handleJoinForDinner = async (e: React.MouseEvent) => {
    e.stopPropagation();
    console.log('Join for Dinner clicked for:', character.name);
    
    // If user is not authenticated, sign in anonymously first
    if (!user) {
      console.log('User not authenticated, signing in anonymously...');
      setIsSigningIn(true);
      
      try {
        const { error } = await signInAnonymously();
        if (error) {
          console.error('Anonymous sign-in failed:', error);
          // Fallback to waitlist modal if anonymous sign-in fails
          setShowWaitlistModal(true);
          setIsSigningIn(false);
          return;
        }
        
        console.log('Anonymous sign-in successful, navigating to chat');
        // Navigation will happen automatically after successful sign-in due to auth state change
        navigate(`/chat/${character.id}`, { 
          state: { character },
          replace: false 
        });
      } catch (error) {
        console.error('Error during anonymous sign-in:', error);
        setShowWaitlistModal(true);
      }
      
      setIsSigningIn(false);
      return;
    }

    // User is authenticated - proceed to chat
    console.log('User authenticated, navigating to chat');
    navigate(`/chat/${character.id}`, { 
      state: { character },
      replace: false 
    });
  };

  const firstName = character.name.split(' ')[0];

  return (
    <>
      <Card 
        onClick={handleCardClick}
        className={`relative overflow-hidden transition-all duration-500 hover:scale-105 cursor-pointer group character-card-deco h-full ${
          isSelected 
            ? 'bg-noir-burgundy border-noir-amber shadow-2xl animate-glow-pulse' 
            : 'bg-noir-charcoal border-noir-shadow hover:border-noir-amber/50'
        }`}
      >
        <div className="absolute inset-0 bg-gradient-to-br from-noir-amber/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        <div className="relative p-6 flex flex-col h-full content-overlay">
          {/* Character Avatar */}
          <div className="flex justify-center mb-4">
            <div className={`w-20 h-20 rounded-full flex items-center justify-center text-3xl font-bold border-2 character-avatar-frame ${
              isSelected 
                ? 'bg-noir-amber text-noir-charcoal border-noir-cream' 
                : 'bg-noir-shadow text-noir-amber border-noir-amber/50'
            }`}>
              {character.avatar}
            </div>
          </div>

          {/* Character Name & Title */}
          <div className="text-center space-y-2 mb-4">
            <h3 className={`font-hollywood text-xl font-semibold ${
              isSelected ? 'text-noir-cream' : 'text-noir-amber'
            }`}>
              {character.name}
            </h3>
            <p className={`text-sm font-medium ${
              isSelected ? 'text-noir-cream/80' : 'text-noir-cream/60'
            }`}>
              {character.title}
            </p>
          </div>

          {/* Art Deco Content Divider */}
          <div className="art-deco-divider mb-4" style={{ height: '1px' }}></div>

          {/* Description - This will grow to fill available space */}
          <div className="flex-grow space-y-4 mb-4">
            <p className={`text-sm leading-relaxed ${
              isSelected ? 'text-noir-cream/90' : 'text-noir-cream/70'
            }`}>
              {character.description}
            </p>

            {/* Inspiration */}
            <div className="space-y-1">
              <p className={`text-xs font-medium ${
                isSelected ? 'text-noir-amber' : 'text-noir-amber/80'
              }`}>
                Inspired by {character.inspiration}
              </p>
            </div>

            {/* Signature Quote */}
            <blockquote className={`text-xs italic border-l-2 pl-3 ${
              isSelected 
                ? 'border-noir-amber text-noir-cream/80' 
                : 'border-noir-amber/50 text-noir-cream/60'
            }`}>
              "{character.signature}"
            </blockquote>
          </div>

          {/* Buttons - These will stick to the bottom */}
          <div className="space-y-2 mt-auto">
            {/* About Button */}
            <Button
              onClick={handleAbout}
              className="w-full bg-noir-shadow border border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20 hover:text-noir-cream hover:border-noir-amber transition-all duration-300 art-deco-button"
              variant="outline"
            >
              About {firstName}
            </Button>

            {/* Join for Dinner Button */}
            <Button
              onClick={handleJoinForDinner}
              disabled={isSigningIn}
              variant="noir-burgundy"
              className="w-full font-semibold px-8 py-3 text-lg art-deco-button"
            >
              {isSigningIn 
                ? 'Joining...'
                : user 
                ? (isSelected ? 'Continue Conversation' : 'Join for Dinner')
                : 'Join for Dinner'
              }
            </Button>
          </div>
        </div>
      </Card>

      {/* Waitlist Modal */}
      <WaitlistModal
        isOpen={showWaitlistModal}
        onClose={() => setShowWaitlistModal(false)}
        characterName={character.name}
      />
    </>
  );
};

export default CharacterCard;
