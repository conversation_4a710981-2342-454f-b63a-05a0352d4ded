
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { X } from 'lucide-react';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAuth: (email: string, password: string, isSignUp: boolean) => void;
}

const AuthModal = ({ isOpen, onClose, onAuth }: AuthModalProps) => {
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      await onAuth(email, password, isSignUp);
      onClose();
    } catch (error) {
      console.error('Auth error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="absolute inset-0 bg-noir-deep-charcoal/80 backdrop-blur-sm" onClick={onClose}></div>
      
      <Card className="relative w-full max-w-md mx-4 bg-noir-charcoal border-noir-amber/30 shadow-2xl">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="font-hollywood text-2xl font-semibold text-noir-amber">
              {isSignUp ? 'Join the Table' : 'Welcome Back'}
            </h2>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-noir-cream/60 hover:text-noir-amber"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-noir-cream/80">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-noir-cream/80">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                required
              />
            </div>

            <Button
              type="submit"
              disabled={loading}
              className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
            >
              {loading ? 'Please wait...' : (isSignUp ? 'Create Account' : 'Sign In')}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-noir-cream/60 text-sm">
              {isSignUp ? 'Already have an account?' : "Don't have an account?"}
            </p>
            <Button
              onClick={() => setIsSignUp(!isSignUp)}
              variant="link"
              className="text-noir-amber hover:text-noir-warm-amber p-0 h-auto"
            >
              {isSignUp ? 'Sign in instead' : 'Create one here'}
            </Button>
          </div>

          <div className="mt-6 p-4 bg-noir-shadow/50 rounded-lg border border-noir-amber/20">
            <h3 className="text-noir-amber text-sm font-medium mb-2">Premium Features</h3>
            <ul className="text-noir-cream/70 text-xs space-y-1">
              <li>• Unlimited conversations with all characters</li>
              <li>• Conversation history & transcripts</li>
              <li>• Advanced character personalities</li>
              <li>• Export conversations for screenwriting</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AuthModal;
