import React, { useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ArrowLeft, Award, Film, BookOpen, Users, Loader2 } from 'lucide-react';
import { useCharacters, type Character } from '@/hooks/useCharacters';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';

const CharacterDetail = () => {
  const { characterId } = useParams();
  const navigate = useNavigate();
  const { user, signInAnonymously } = useAuth();
  const { data: characters, isLoading, error } = useCharacters();
  const [isSigningIn, setIsSigningIn] = React.useState(false);

  // Find the character
  const character = characters?.find(c => c.id === characterId);

  // Ensure we start at the top of the page
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [characterId]);

  const handleBack = () => {
    navigate('/');
  };

  const handleStartConversation = async (character: Character) => {
    if (!user) {
      console.log('User not authenticated, signing in anonymously...');
      setIsSigningIn(true);
      
      try {
        const { error } = await signInAnonymously();
        if (error) {
          console.error('Anonymous sign-in failed:', error);
          // Fallback to waitlist modal if anonymous sign-in fails
          navigate('/', { state: { showWaitlistFor: character } });
          setIsSigningIn(false);
          return;
        }
        
        console.log('Anonymous sign-in successful, navigating to chat');
        navigate(`/chat/${character.id}`, { state: { character } });
      } catch (error) {
        console.error('Error during anonymous sign-in:', error);
        navigate('/', { state: { showWaitlistFor: character } });
      }
      
      setIsSigningIn(false);
      return;
    }

    // Navigate to chat
    navigate(`/chat/${character.id}`, { state: { character } });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-noir-gradient">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="w-8 h-8 text-noir-amber animate-spin" />
            <p className="text-noir-cream/70">Loading character...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error or character not found
  if (error || !character) {
    return (
      <div className="min-h-screen bg-noir-gradient">
        <Header />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center space-y-4">
            <p className="text-red-400">
              {error ? 'Failed to load character' : 'Character not found'}
            </p>
            <Button 
              onClick={handleBack}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Characters
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Character-specific data
  const characterData = {
    'max-sterling': {
      bio: "European filmmaker who fled the old country in the 1930s and revolutionized Hollywood storytelling with sharp wit and unflinching honesty about human nature.",
      quote: character.signature,
      citation: "Max's Philosophy on Character",
      stats: [
        { icon: Award, label: "Academy Awards", value: "7 nominations, 2 wins" },
        { icon: Film, label: "Films Directed", value: "23 films (1940-1975)" },
        { icon: BookOpen, label: "Screenplays", value: "31 original works" },
        { icon: Users, label: "Specialty", value: "Character Development" }
      ],
      expertise: [
        "Sharp dialogue that reveals character subtext",
        "Creating flawed, compelling protagonists", 
        "Cynical takes on the American Dream",
        "European storytelling techniques",
        "Surviving Hollywood politics and studio systems",
        "Balancing humor with dramatic truth"
      ],
      knownFor: [
        "Sharp dialogue that reveals character subtext",
        "Flawed protagonists who feel authentically human",
        "Cynical yet compassionate worldview",
        "Blending European and American storytelling"
      ],
      teachingStyle: "Max challenges students with tough questions and honest feedback. He believes in learning through doing, not just theory. Expect wit, wisdom, and the occasional glass of whiskey."
    },
    'danny-cross': {
      bio: "Former private investigator turned legendary action screenwriter who proved you could be smart AND blow things up. Master of buddy cop dynamics and hard-boiled dialogue with a playful, mischievous sense of humor.",
      quote: character.signature,
      citation: "Danny's Philosophy on Character",
      stats: [
        { icon: Award, label: "Academy Awards", value: "3 nominations, 1 win for Best Original Screenplay" },
        { icon: Film, label: "Films Directed", value: "8 films (1987-2018)" },
        { icon: BookOpen, label: "Screenplays", value: "12 original works" },
        { icon: Users, label: "Specialty", value: "Action-Thriller Writing" }
      ],
      expertise: [
        "Hard-boiled dialogue that crackles with wit",
        "Building action scenes that reveal character",
        "Mastering the buddy cop dynamic",
        "Balancing humor with genuine stakes",
        "Writing \"two people in over their heads\" scenarios"
      ],
      knownFor: [
        "Quick, snappy dialogue with unexpected humor",
        "Labyrinthine crime plots that make sense",
        "Action sequences driven by character conflict",
        "Meta storytelling and Hollywood insider wisdom"
      ],
      teachingStyle: "Danny brings street-smart wisdom and investigative instincts to screenwriting. He'll teach you to think like a detective when plotting and to write dialogue that sounds like real people under pressure."
    },
    'ruby-cohen': {
      bio: "A brilliant romantic comedy screenwriter who revolutionized how women's voices are heard in Hollywood. Ruby mastered the art of sharp, witty dialogue and turned everyday relationships into cinematic gold. Known for her keen observations about love, friendship, and the complexities of modern romance.",
      quote: character.signature,
      citation: "Ruby's Philosophy on Romance",
      stats: [
        { icon: Award, label: "Academy Awards", value: "3 nominations for Best Original Screenplay" },
        { icon: Film, label: "Films Written", value: "12 romantic comedies (1978-2012)" },
        { icon: BookOpen, label: "Screenplays", value: "18 original works" },
        { icon: Users, label: "Specialty", value: "Romantic Comedy & Women's Stories" }
      ],
      expertise: [
        "Writing authentic dialogue that reveals character",
        "Crafting romantic tension that feels real, not forced",
        "Creating strong female protagonists who drive the story",
        "Balancing humor with genuine emotional stakes",
        "The secrets of \"meet-cute\" scenes that actually work"
      ],
      knownFor: [
        "Razor-sharp wit with emotional depth",
        "Making ordinary moments feel extraordinary",
        "Understanding the female perspective in romance",
        "Dialogue that sounds like real people talking",
        "Stories about friendship, love, and finding yourself"
      ],
      teachingStyle: "Ruby encourages writers to find the universal truth in personal moments. She believes in authentic emotion and teaches that the best romantic comedies are really about people becoming better versions of themselves."
    },
    'holly-winters': {
      bio: "The reigning queen of feel-good Christmas romance who has written over 30 heartwarming holiday movies. Holly mastered the art of creating magical small-town Christmas stories that make millions of viewers believe in love, hope, and holiday miracles. She knows exactly how to craft the perfect meet-cute, the inevitable Christmas crisis, and the happily-ever-after that audiences crave.",
      quote: character.signature,
      citation: "Holly's Philosophy on Holiday Magic",
      stats: [
        { icon: Award, label: "Holiday Movies", value: "35+ Christmas and holiday films" },
        { icon: Film, label: "Networks", value: "Hallmark, Lifetime, Netflix" },
        { icon: BookOpen, label: "Screenplays", value: "30+ romantic Christmas comedies" },
        { icon: Users, label: "Specialty", value: "Christmas Romance & Feel-Good Stories" }
      ],
      expertise: [
        "The proven Hallmark Christmas movie formula that works every time",
        "How to create compelling meet-cutes in holiday settings",
        "Writing dialogue that feels warm, cozy, and authentic",
        "Balancing romance with Christmas magic and family themes",
        "Creating small-town charm that feels genuine, not cheesy"
      ],
      knownFor: [
        "The \"18-minute rule\" - when the almost-kiss happens",
        "Christmas-in-every-scene visual storytelling",
        "Creating career women who rediscover what really matters",
        "Small-town vs. big-city character dynamics",
        "Making viewers cry happy tears by the end credits"
      ],
      teachingStyle: "Holly believes in the magic of Christmas storytelling and teaches that these movies are comfort food for the soul. She's practical about the business but never cynical about the emotions, showing writers how to deliver hope and joy through formula."
    },
    'victor-kane': {
      bio: "The prolific master of modern horror who has terrified and captivated readers for decades with over 60 novels and countless screenplays. Victor transformed horror from cheap thrills into compelling human drama, proving that the best scares come from authentic characters facing impossible situations. He knows how to make readers care deeply about people, then put them through absolute hell.",
      quote: character.signature,
      citation: "Victor's Philosophy on Fear",
      stats: [
        { icon: Award, label: "Horror Works", value: "60+ novels, dozens of film adaptations" },
        { icon: Film, label: "Box Office", value: "Multiple billion-dollar horror franchises" },
        { icon: BookOpen, label: "Specialty", value: "Supernatural horror grounded in small-town reality" },
        { icon: Users, label: "Recognition", value: "\"The King of Horror\" by critics and fans worldwide" }
      ],
      expertise: [
        "How to build genuine fear through character psychology, not cheap scares",
        "The art of making supernatural elements feel believable and inevitable",
        "Writing authentic dialogue across different social classes and age groups",
        "Balancing daily writing discipline with creative inspiration",
        "Creating iconic, memorable horror scenes that haunt audiences"
      ],
      knownFor: [
        "The \"ordinary becomes extraordinary\" approach to horror premises",
        "Making child characters authentic and compelling",
        "Small-town settings that feel lived-in and real",
        "Monsters that represent deeper human fears",
        "Writing every single day, no matter what"
      ],
      teachingStyle: "Victor believes in hard work over inspiration and teaches that talent without discipline is just wasted potential. He's down-to-earth and practical, focusing on the craft of daily writing and authentic character development. Expect honest feedback and real-world wisdom from someone who's built a career on scaring people in the best possible way."
    },
    'jake-abbott': {
      bio: "The architect of modern R-rated comedy who revolutionized how we laugh at life's awkward truths. Jake has produced and directed over 25 hit comedies that blend raunchy humor with genuine heart, proving that the funniest moments come from real human vulnerability. He discovered and launched countless comedy careers while creating a new template for grown-up laughs.",
      quote: character.signature,
      citation: "Jake's Philosophy on Authentic Humor",
      stats: [
        { icon: Award, label: "Comedy Films", value: "25+ produced/directed R-rated comedies" },
        { icon: Film, label: "Box Office", value: "Over $3 billion in worldwide comedy hits" },
        { icon: BookOpen, label: "Specialty", value: "Character-driven improvisation and authentic dialogue" },
        { icon: Users, label: "Recognition", value: "\"The King of Modern Comedy\" by industry peers" }
      ],
      expertise: [
        "How to mine personal experience for universal comedy gold",
        "The art of improvisation within structured storytelling",
        "Writing authentic dialogue that feels natural, not scripted",
        "Balancing raunchy humor with genuine emotional moments",
        "Developing ensemble comedies where everyone gets great moments"
      ],
      knownFor: [
        "The \"comedy of discomfort\" - finding humor in awkward situations",
        "Letting actors improvise while maintaining story structure",
        "Creating flawed, lovable characters who grow through humor",
        "Making R-rated comedies that adults actually relate to",
        "Discovering and nurturing new comedy talent"
      ],
      teachingStyle: "Jake believes comedy is just drama with better timing and teaches that the best laughs come from characters we care about in situations we recognize. He's collaborative and encouraging, always looking for the authentic moment that makes people laugh and feel understood at the same time. Expect honest, supportive feedback with plenty of humor about the writing process itself."
    },
    'luke-starr': {
      bio: "The visionary who transformed sci-fi from B-movie genre into the biggest blockbuster phenomenon in cinema history. Luke created the most beloved space opera of all time, revolutionizing both storytelling and filmmaking technology. He mastered the art of blending ancient mythology with futuristic adventure, proving that the best sci-fi stories are timeless tales of good versus evil told among the stars.",
      quote: character.signature,
      citation: "Luke's Philosophy on Mythological Storytelling",
      stats: [
        { icon: Award, label: "Sci-Fi Films", value: "Created the most successful space opera franchise in history" },
        { icon: Film, label: "Box Office", value: "Over $10 billion in worldwide sci-fi revenues" },
        { icon: BookOpen, label: "Specialty", value: "Mythological structure, world-building, and transmedia storytelling" },
        { icon: Users, label: "Recognition", value: "\"The Father of Modern Sci-Fi Cinema\" by industry peers" }
      ],
      expertise: [
        "How to structure stories using the timeless hero's journey",
        "Creating believable alien worlds, creatures, and technologies",
        "Balancing mysticism with science in compelling ways",
        "Building franchises and expanded universes that last decades",
        "Writing sci-fi that appeals to all ages without dumbing down"
      ],
      knownFor: [
        "The hero's journey as the backbone of space adventure",
        "Creating iconic characters that become cultural legends",
        "Seamlessly blending practical effects with storytelling",
        "Making the fantastic feel lived-in and authentic",
        "Building detailed mythologies that span generations"
      ],
      teachingStyle: "Luke is thoughtful and philosophical, seeing sci-fi as a vehicle for exploring timeless themes about good, evil, and the human condition. He emphasizes understanding classical story structure and universal truths, teaching that technology is just the costume - the story is about the eternal human struggle."
    },
    'tessa-ward': {
      bio: "The razor-sharp comedy writer who elevated television satire to an art form while breaking barriers for women in comedy. Tessa has created and written multiple Emmy-winning series that blend workplace humor with brilliant social commentary. She proved that the smartest comedy comes from observing the absurdities of real life, then making them hilariously relatable.",
      quote: character.signature,
      citation: "Tessa's Philosophy on Satirical Comedy",
      stats: [
        { icon: Award, label: "TV Series", value: "Created and wrote 5+ critically acclaimed comedy series" },
        { icon: Film, label: "Awards", value: "Multiple Emmy and Golden Globe wins for comedy writing" },
        { icon: BookOpen, label: "Specialty", value: "Workplace satire, character-driven ensemble comedy, and smart social commentary" },
        { icon: Users, label: "Recognition", value: "\"The Queen of Television Comedy Writing\" by industry peers" }
      ],
      expertise: [
        "How to write sharp, quotable dialogue that reveals character",
        "Creating workplace comedies that satirize real professional environments",
        "Balancing ensemble casts where every character gets great moments",
        "Writing comedy that's both hilarious and socially aware",
        "Developing series that sustain laughs across multiple seasons"
      ],
      knownFor: [
        "The \"smart comedy\" approach - never talking down to audiences",
        "Creating flawed but lovable female protagonists",
        "Workplace dynamics that feel authentic yet absurd",
        "Pop culture references that enhance rather than replace jokes",
        "Writing rooms that develop characters through comedy"
      ],
      teachingStyle: "Tessa is wickedly smart and self-deprecating, finding comedy in the mundane details of modern life. She's supportive but direct, believing that good comedy writing requires both talent and relentless hard work. Expect sharp insights mixed with practical TV writing advice, all delivered with her signature wit."
    }
  };

  const currentData = characterData[character.id as keyof typeof characterData] || characterData['max-sterling'];

  return (
    <div className="min-h-screen bg-noir-gradient">
      <Header />
      
      {/* Back Button Header */}
      <div className="bg-noir-charcoal/95 border-b border-noir-amber/20 p-4">
        <div className="max-w-6xl mx-auto flex items-center space-x-4">
          <Button
            onClick={handleBack}
            variant="ghost"
            className="text-noir-amber hover:text-noir-warm-amber"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Table
          </Button>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-noir-deep-charcoal via-noir-charcoal to-noir-shadow"></div>
        <div className="relative max-w-6xl mx-auto px-6 py-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Character Portrait */}
            <div className="text-center lg:text-left">
              <div className="relative inline-block mb-6">
                <div className="w-64 h-64 mx-auto lg:mx-0 rounded-full bg-gradient-to-br from-noir-burgundy to-noir-shadow border-4 border-noir-amber flex items-center justify-center text-6xl font-bold text-noir-amber shadow-2xl">
                  {character.avatar}
                </div>
                <div className="absolute -bottom-2 -right-2 w-16 h-16 bg-noir-amber rounded-full flex items-center justify-center border-4 border-noir-charcoal">
                  <Award className="w-8 h-8 text-noir-charcoal" />
                </div>
              </div>
            </div>

            {/* Character Info */}
            <div className="space-y-6">
              <div>
                <h1 className="font-hollywood text-4xl lg:text-5xl font-bold text-noir-amber amber-glow mb-2">
                  {character.name}
                </h1>
                <p className="text-xl text-noir-warm-amber font-medium mb-4">
                  {character.title}
                </p>
                <p className="text-lg text-noir-cream/80 leading-relaxed">
                  {currentData.bio}
                </p>
              </div>

              <blockquote className="border-l-4 border-noir-amber pl-6 italic text-noir-cream/90">
                <p className="text-lg mb-2">"{currentData.quote}"</p>
                <cite className="text-sm text-noir-amber">— {currentData.citation}</cite>
              </blockquote>

              <Button
                onClick={() => handleStartConversation(character)}
                disabled={isSigningIn}
                className={`font-semibold px-8 py-4 text-lg art-deco-button ${
                  isSigningIn
                    ? 'bg-noir-shadow border border-noir-amber/50 text-noir-amber/70 cursor-not-allowed'
                    : 'bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber'
                }`}
                size="lg"
              >
                {isSigningIn
                  ? 'Joining...'
                  : user 
                  ? `Join ${character.name.split(' ')[0]} for Dinner`
                  : `Join ${character.name.split(' ')[0]} for Dinner`
                }
              </Button>
              
              {!user && !isSigningIn && (
                <p className="text-sm text-noir-cream/60">
                  Start your conversation immediately - no waiting required
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Career Statistics */}
      <div className="max-w-6xl mx-auto px-6 py-16">
        <h2 className="font-hollywood text-3xl font-semibold text-noir-amber text-center mb-12">
          Career Achievements
        </h2>
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {currentData.stats.map((stat, index) => (
            <Card key={index} className="bg-noir-charcoal/80 border-noir-amber/30 p-6 text-center hover:border-noir-amber transition-all duration-300">
              <stat.icon className="w-8 h-8 text-noir-amber mx-auto mb-4" />
              <h3 className="font-semibold text-noir-cream mb-2">{stat.label}</h3>
              <p className="text-noir-cream/70 text-sm">{stat.value}</p>
            </Card>
          ))}
        </div>
      </div>

      {/* What You'll Learn */}
      <div className="bg-noir-charcoal/30 border-t border-noir-amber/20">
        <div className="max-w-6xl mx-auto px-6 py-16">
          <div className="grid lg:grid-cols-2 gap-12">
            <div>
              <h2 className="font-hollywood text-3xl font-semibold text-noir-amber mb-6">
                What You'll Learn from {character.name.split(' ')[0]}
              </h2>
              <p className="text-lg text-noir-cream/80 mb-8 leading-relaxed">
                {character.name.split(' ')[0]} brings decades of Hollywood wisdom, {character.id === 'danny-cross' ? 'investigative instincts' : character.id === 'holly-winters' ? 'Christmas movie magic' : character.id === 'victor-kane' ? 'horror mastery and daily writing discipline' : character.id === 'jake-abbott' ? 'comedy genius and collaborative spirit' : character.id === 'tessa-ward' ? 'television comedy expertise and satirical wit' : 'European storytelling traditions'}, and hard-earned insights about the craft of screenwriting. {character.id === 'holly-winters' || character.id === 'tessa-ward' ? 'Her' : 'His'} conversations blend technical expertise with {character.id === 'holly-winters' ? 'heartwarming wisdom' : character.id === 'victor-kane' ? 'practical, down-to-earth guidance' : character.id === 'jake-abbott' ? 'authentic humor and human insight' : character.id === 'tessa-ward' ? 'sharp wit and professional insight' : 'philosophical depth'}.
              </p>
              
              <div className="space-y-3">
                {currentData.expertise.map((skill, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-noir-amber rounded-full mt-2 flex-shrink-0"></div>
                    <p className="text-noir-cream/90">{skill}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-6">
              <Card className="bg-noir-burgundy/20 border-noir-amber/30 p-6">
                <h3 className="font-hollywood text-xl font-semibold text-noir-amber mb-4">
                  Known For
                </h3>
                <div className="space-y-3 text-noir-cream/80">
                  {currentData.knownFor.map((item, index) => (
                    <p key={index}>• {item}</p>
                  ))}
                </div>
              </Card>

              <Card className="bg-noir-shadow/50 border-noir-amber/20 p-6">
                <h3 className="font-hollywood text-xl font-semibold text-noir-amber mb-4">
                  Teaching Style
                </h3>
                <p className="text-noir-cream/80">
                  {currentData.teachingStyle}
                </p>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Final CTA */}
      <div className="bg-noir-deep-charcoal border-t border-noir-amber/20">
        <div className="max-w-4xl mx-auto px-6 py-16 text-center">
          <h2 className="font-hollywood text-3xl font-semibold text-noir-amber mb-4">
            {isSigningIn ? 'Preparing Your Table...' : user ? 'Ready to Learn from a Master?' : 'Start Your Journey'}
          </h2>
          <p className="text-lg text-noir-cream/80 mb-8 max-w-2xl mx-auto">
            {isSigningIn
              ? 'We\'re setting up your conversation with the master...'
              : user 
              ? `Join ${character.name} at ${character.id === 'holly-winters' || character.id === 'tessa-ward' ? 'her' : 'his'} corner table for an intimate conversation about the art and craft of screenwriting. Bring your questions, your scripts, and your curiosity.`
              : `Start your conversation with ${character.name} immediately. No waitlists, no delays - just click below to begin your screenwriting journey.`
            }
          </p>
          <Button
            onClick={() => handleStartConversation(character)}
            disabled={isSigningIn}
            variant={isSigningIn ? "outline" : "noir-burgundy"}
            className={`font-semibold px-12 py-4 text-xl art-deco-button ${
              isSigningIn ? 'cursor-not-allowed opacity-70' : ''
            }`}
            size="lg"
          >
            {isSigningIn ? 'Joining...' : user ? 'Reserve Your Seat' : `Join ${character.name.split(' ')[0]} for Dinner`}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CharacterDetail;

