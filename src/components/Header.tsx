import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { User, Settings, Calendar, CreditCard } from 'lucide-react';
import AuthModal from './AuthModal';
import WaitlistModal from './WaitlistModal';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const Header = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showWaitlistModal, setShowWaitlistModal] = useState(false);

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  const openWaitlistModal = () => {
    setShowWaitlistModal(true);
    setShowAuthModal(false);
  };

  return (
    <>
      <header className="bg-noir-charcoal/95 backdrop-blur-sm border-b border-noir-amber/20 py-4 px-6 header-deco">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div 
            className="flex items-center cursor-pointer" 
            onClick={() => navigate('/')}
          >
            <img 
              src="/lovable-uploads/05989f44-c43c-48ae-bfc2-8b8508a9d550.png" 
              alt="Hollywood Table" 
              className="h-8 w-auto"
            />
          </div>
          
          <nav className="flex items-center space-x-6">
            {user ? (
              <div className="flex items-center space-x-4">
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/conversations')}
                  className="text-noir-cream hover:text-noir-amber hover:bg-noir-amber/20 font-source-sans transition-all duration-300"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  My Conversations
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="ghost" 
                      className="text-noir-cream hover:text-noir-amber hover:bg-noir-amber/20 font-source-sans transition-all duration-300"
                    >
                      <User className="w-4 h-4 mr-2" />
                      Account
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent 
                    className="bg-noir-charcoal border-noir-amber/30 text-noir-cream"
                    align="end"
                  >
                    <DropdownMenuItem 
                      onClick={() => navigate('/account')}
                      className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      Account Settings
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => navigate('/pricing')}
                      className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber"
                    >
                      <CreditCard className="w-4 h-4 mr-2" />
                      Subscription
                    </DropdownMenuItem>
                    <DropdownMenuSeparator className="bg-noir-amber/20" />
                    <DropdownMenuItem 
                      onClick={handleSignOut}
                      className="text-noir-cream hover:bg-noir-amber/20 hover:text-noir-amber focus:bg-noir-amber/20 focus:text-noir-amber"
                    >
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Button 
                  variant="ghost" 
                  onClick={() => navigate('/pricing')}
                  className="text-noir-cream hover:text-noir-amber hover:bg-noir-amber/20 font-source-sans transition-all duration-300"
                >
                  Pricing
                </Button>
                <Button 
                  variant="ghost" 
                  onClick={() => setShowAuthModal(true)}
                  className="text-noir-cream hover:text-noir-amber hover:bg-noir-amber/20 font-source-sans transition-all duration-300"
                >
                  Sign In
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowAuthModal(true)}
                  className="border-noir-amber text-noir-charcoal bg-noir-amber hover:bg-noir-amber/80 art-deco-button font-source-sans transition-all duration-300"
                >
                  Sign Up
                </Button>
              </div>
            )}
          </nav>
        </div>
      </header>

      <AuthModal 
        isOpen={showAuthModal} 
        onClose={() => setShowAuthModal(false)}
        onOpenWaitlist={openWaitlistModal}
      />
      
      <WaitlistModal 
        isOpen={showWaitlistModal} 
        onClose={() => setShowWaitlistModal(false)} 
      />
    </>
  );
};

export default Header;
