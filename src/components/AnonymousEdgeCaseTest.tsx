import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useUsage } from '@/contexts/UsageContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { AlertTriangle, Users, MessageCircle, Database, RefreshCw } from 'lucide-react';

interface EdgeCaseTestResult {
  test: string;
  status: 'pending' | 'success' | 'warning' | 'error';
  message: string;
  data?: any;
}

const AnonymousEdgeCaseTest: React.FC = () => {
  const { user, isAnonymous } = useAuth();
  const { usageData, refreshUsage } = useUsage();
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<EdgeCaseTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addTestResult = (result: EdgeCaseTestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runEdgeCaseTests = async () => {
    if (!user) {
      toast({
        title: "No user found",
        description: "Please sign in to run edge case tests",
        variant: "destructive"
      });
      return;
    }

    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Multiple rapid message sending
      await testRapidMessageSending();

      // Test 2: Concurrent usage checking
      await testConcurrentUsageChecking();

      // Test 3: Database consistency after multiple operations
      await testDatabaseConsistency();

      // Test 4: Anonymous user session persistence
      await testSessionPersistence();

      // Test 5: Usage tracking with deleted messages
      await testUsageWithDeletedMessages();

      // Test 6: PAYG credit edge cases
      await testPaygCreditEdgeCases();

      // Test 7: Anonymous user limit boundary testing
      await testAnonymousLimitBoundary();

    } catch (error) {
      addTestResult({
        test: "Edge Case Test Execution",
        status: 'error',
        message: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    } finally {
      setIsRunning(false);
    }
  };

  const testRapidMessageSending = async () => {
    addTestResult({
      test: "Rapid Message Sending",
      status: 'pending',
      message: "Testing rapid consecutive message sending..."
    });

    try {
      const testConversationId = await createTestConversation();
      if (!testConversationId) throw new Error('Failed to create test conversation');

      // Send 5 messages rapidly
      const promises = [];
      for (let i = 1; i <= 5; i++) {
        promises.push(
          supabase.from('messages').insert({
            conversation_id: testConversationId,
            sender_type: 'user',
            content: `Rapid message ${i} - ${Date.now()}`
          })
        );
      }

      const results = await Promise.allSettled(promises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const errorCount = results.filter(r => r.status === 'rejected').length;

      // Check final usage count
      await refreshUsage();
      
      addTestResult({
        test: "Rapid Message Sending",
        status: errorCount === 0 ? 'success' : 'warning',
        message: `Sent ${successCount} messages rapidly, ${errorCount} failed. Usage tracking should be consistent.`,
        data: { 
          successCount, 
          errorCount, 
          finalUsage: usageData?.messages_used,
          results: results.map(r => r.status)
        }
      });

      // Cleanup
      await cleanupTestConversation(testConversationId);

    } catch (error) {
      addTestResult({
        test: "Rapid Message Sending",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testConcurrentUsageChecking = async () => {
    addTestResult({
      test: "Concurrent Usage Checking",
      status: 'pending',
      message: "Testing concurrent usage limit checks..."
    });

    try {
      // Make multiple concurrent usage check requests
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(
          supabase.functions.invoke('api/usage/check-limits', {
            method: 'POST',
            body: { action_type: 'message' }
          })
        );
      }

      const results = await Promise.allSettled(promises);
      const successResults = results
        .filter(r => r.status === 'fulfilled')
        .map(r => (r as PromiseFulfilledResult<any>).value);

      // Check if all results are consistent
      const firstResult = successResults[0]?.data;
      const allConsistent = successResults.every(result => 
        result.data?.subscription_info?.messages_used === firstResult?.subscription_info?.messages_used
      );

      addTestResult({
        test: "Concurrent Usage Checking",
        status: allConsistent ? 'success' : 'warning',
        message: allConsistent 
          ? 'All concurrent usage checks returned consistent results'
          : 'Concurrent usage checks returned inconsistent results',
        data: { 
          totalRequests: promises.length,
          successfulRequests: successResults.length,
          allConsistent,
          results: successResults.map(r => r.data?.subscription_info?.messages_used)
        }
      });

    } catch (error) {
      addTestResult({
        test: "Concurrent Usage Checking",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testDatabaseConsistency = async () => {
    addTestResult({
      test: "Database Consistency",
      status: 'pending',
      message: "Testing database consistency after multiple operations..."
    });

    try {
      // Get current subscription data
      const { data: subscription, error: subError } = await supabase
        .from('user_subscriptions')
        .select('*')
        .eq('user_id', user!.id)
        .single();

      if (subError) throw subError;

      // Count actual messages in current period
      const { data: messageCount, error: countError } = await supabase
        .rpc('count_user_messages_in_period', {
          user_uuid: user!.id,
          period_start: subscription.current_period_start,
          period_end: subscription.current_period_end
        });

      if (countError) {
        // If function doesn't exist, count manually
        const { count, error: manualCountError } = await supabase
          .from('messages')
          .select('*', { count: 'exact', head: true })
          .eq('sender_type', 'user')
          .gte('created_at', subscription.current_period_start)
          .lte('created_at', subscription.current_period_end)
          .in('conversation_id', 
            await supabase
              .from('conversations')
              .select('id')
              .eq('user_id', user!.id)
              .then(({ data }) => data?.map(c => c.id) || [])
          );

        if (manualCountError) throw manualCountError;
        
        const isConsistent = count === subscription.messages_used_this_period;
        
        addTestResult({
          test: "Database Consistency",
          status: isConsistent ? 'success' : 'warning',
          message: isConsistent 
            ? 'Database message count matches subscription record'
            : 'Database message count does not match subscription record',
          data: { 
            subscriptionCount: subscription.messages_used_this_period,
            actualCount: count,
            isConsistent
          }
        });
      } else {
        const isConsistent = messageCount === subscription.messages_used_this_period;
        
        addTestResult({
          test: "Database Consistency",
          status: isConsistent ? 'success' : 'warning',
          message: isConsistent 
            ? 'Database message count matches subscription record'
            : 'Database message count does not match subscription record',
          data: { 
            subscriptionCount: subscription.messages_used_this_period,
            actualCount: messageCount,
            isConsistent
          }
        });
      }

    } catch (error) {
      addTestResult({
        test: "Database Consistency",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testSessionPersistence = async () => {
    addTestResult({
      test: "Session Persistence",
      status: 'pending',
      message: "Testing anonymous user session persistence..."
    });

    try {
      // Get current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) throw sessionError;
      if (!session) throw new Error('No active session');

      // Test session refresh
      const { data: { session: refreshedSession }, error: refreshError } = 
        await supabase.auth.refreshSession();

      if (refreshError) throw refreshError;

      const sessionPersisted = refreshedSession?.user?.id === session.user.id;
      const anonymousStatusPersisted = 
        refreshedSession?.user?.user_metadata?.is_anonymous === session.user.user_metadata?.is_anonymous;

      addTestResult({
        test: "Session Persistence",
        status: sessionPersisted && anonymousStatusPersisted ? 'success' : 'warning',
        message: sessionPersisted && anonymousStatusPersisted
          ? 'Anonymous user session persisted correctly after refresh'
          : 'Session persistence issues detected',
        data: { 
          sessionPersisted,
          anonymousStatusPersisted,
          originalUserId: session.user.id,
          refreshedUserId: refreshedSession?.user?.id,
          originalAnonymous: session.user.user_metadata?.is_anonymous,
          refreshedAnonymous: refreshedSession?.user?.user_metadata?.is_anonymous
        }
      });

    } catch (error) {
      addTestResult({
        test: "Session Persistence",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testUsageWithDeletedMessages = async () => {
    addTestResult({
      test: "Usage with Deleted Messages",
      status: 'pending',
      message: "Testing usage tracking when messages are deleted..."
    });

    try {
      const testConversationId = await createTestConversation();
      if (!testConversationId) throw new Error('Failed to create test conversation');

      // Send a message
      const { data: message, error: msgError } = await supabase
        .from('messages')
        .insert({
          conversation_id: testConversationId,
          sender_type: 'user',
          content: `Test message for deletion - ${Date.now()}`
        })
        .select()
        .single();

      if (msgError) throw msgError;

      // Check usage after message
      await refreshUsage();
      const usageAfterMessage = usageData?.messages_used;

      // Delete the message
      const { error: deleteError } = await supabase
        .from('messages')
        .delete()
        .eq('id', message.id);

      if (deleteError) throw deleteError;

      // Check usage after deletion
      await refreshUsage();
      const usageAfterDeletion = usageData?.messages_used;

      // Usage should remain the same (messages are counted, not decremented when deleted)
      const usageConsistent = usageAfterMessage === usageAfterDeletion;

      addTestResult({
        test: "Usage with Deleted Messages",
        status: usageConsistent ? 'success' : 'warning',
        message: usageConsistent
          ? 'Usage count correctly maintained after message deletion'
          : 'Usage count changed unexpectedly after message deletion',
        data: { 
          usageAfterMessage,
          usageAfterDeletion,
          usageConsistent
        }
      });

      // Cleanup
      await cleanupTestConversation(testConversationId);

    } catch (error) {
      addTestResult({
        test: "Usage with Deleted Messages",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testPaygCreditEdgeCases = async () => {
    addTestResult({
      test: "PAYG Credit Edge Cases",
      status: 'pending',
      message: "Testing PAYG credit edge cases for anonymous users..."
    });

    try {
      // For anonymous users, PAYG credits should not be usable
      // They should be prompted to upgrade to email instead
      
      const { data: usageCheck, error: usageError } = await supabase.functions.invoke('api/usage/check-limits', {
        method: 'POST',
        body: { action_type: 'message' }
      });

      if (usageError) throw usageError;

      if (isAnonymous) {
        // Anonymous users should be blocked after 3 messages, not offered PAYG
        const correctBehavior = !usageCheck.can_perform && 
          usageCheck.reason?.includes('Anonymous user message limit reached');

        addTestResult({
          test: "PAYG Credit Edge Cases",
          status: correctBehavior ? 'success' : 'warning',
          message: correctBehavior
            ? 'Anonymous users correctly blocked and prompted for email upgrade instead of PAYG'
            : 'Anonymous users may have incorrect PAYG behavior',
          data: { 
            canPerform: usageCheck.can_perform,
            reason: usageCheck.reason,
            isAnonymous,
            correctBehavior
          }
        });
      } else {
        addTestResult({
          test: "PAYG Credit Edge Cases",
          status: 'success',
          message: 'User is not anonymous, PAYG credits should work normally',
          data: { 
            canPerform: usageCheck.can_perform,
            reason: usageCheck.reason,
            isAnonymous
          }
        });
      }

    } catch (error) {
      addTestResult({
        test: "PAYG Credit Edge Cases",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const testAnonymousLimitBoundary = async () => {
    addTestResult({
      test: "Anonymous Limit Boundary",
      status: 'pending',
      message: "Testing anonymous user 3-message limit boundary..."
    });

    try {
      if (!isAnonymous) {
        addTestResult({
          test: "Anonymous Limit Boundary",
          status: 'warning',
          message: 'User is not anonymous, skipping boundary test',
          data: { isAnonymous }
        });
        return;
      }

      // Check current usage
      await refreshUsage();
      const currentUsage = usageData?.messages_used || 0;
      const limit = usageData?.messages_limit || 3;

      // Test at different usage levels
      const testResults = [];
      
      for (let testUsage = 0; testUsage <= 4; testUsage++) {
        // Simulate different usage levels by checking what the API would return
        const { data: usageCheck } = await supabase.functions.invoke('api/usage/check-limits', {
          method: 'POST',
          body: { action_type: 'message' }
        });

        testResults.push({
          usage: testUsage,
          canPerform: usageCheck.can_perform,
          reason: usageCheck.reason,
          expectedBlocked: testUsage >= 3
        });
      }

      const boundaryCorrect = testResults.every(result => 
        result.expectedBlocked ? !result.canPerform : result.canPerform
      );

      addTestResult({
        test: "Anonymous Limit Boundary",
        status: boundaryCorrect ? 'success' : 'warning',
        message: boundaryCorrect
          ? 'Anonymous user 3-message limit boundary working correctly'
          : 'Anonymous user limit boundary may have issues',
        data: { 
          currentUsage,
          limit,
          testResults,
          boundaryCorrect
        }
      });

    } catch (error) {
      addTestResult({
        test: "Anonymous Limit Boundary",
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    }
  };

  const createTestConversation = async (): Promise<string | null> => {
    try {
      const { data: characters } = await supabase
        .from('characters')
        .select('id, name')
        .limit(1);

      if (!characters || characters.length === 0) {
        throw new Error('No characters available for testing');
      }

      const character = characters[0];
      const { data: conversation, error } = await supabase
        .from('conversations')
        .insert({
          character_id: character.id,
          character_name: character.name,
          user_id: user!.id,
          title: `Edge Case Test - ${new Date().toISOString()}`,
          last_message_preview: 'Test conversation for edge case testing',
          message_count: 0,
        })
        .select()
        .single();

      if (error) throw error;
      return conversation.id;
    } catch (error) {
      console.error('Failed to create test conversation:', error);
      return null;
    }
  };

  const cleanupTestConversation = async (conversationId: string) => {
    try {
      await supabase.from('messages').delete().eq('conversation_id', conversationId);
      await supabase.from('conversations').delete().eq('id', conversationId);
    } catch (error) {
      console.error('Failed to cleanup test conversation:', error);
    }
  };

  const getStatusColor = (status: EdgeCaseTestResult['status']) => {
    switch (status) {
      case 'success': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'error': return 'bg-red-500';
      case 'pending': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  if (!user) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardContent className="p-6">
          <p className="text-noir-cream/80">Please sign in to run edge case tests.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardHeader>
          <CardTitle className="text-noir-amber flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Anonymous User Edge Case Tests
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* User Status */}
          <div className="flex items-center space-x-4">
            <Badge variant={isAnonymous ? "default" : "secondary"} className="flex items-center">
              <Users className="w-3 h-3 mr-1" />
              {isAnonymous ? 'Anonymous User' : 'Registered User'}
            </Badge>
            {usageData && (
              <Badge variant="outline" className="flex items-center">
                <MessageCircle className="w-3 h-3 mr-1" />
                {usageData.messages_used}/{usageData.messages_limit} messages
              </Badge>
            )}
          </div>

          {/* Test Controls */}
          <div className="flex space-x-2">
            <Button 
              onClick={runEdgeCaseTests}
              disabled={isRunning}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              {isRunning ? 'Running Edge Case Tests...' : 'Run Edge Case Tests'}
            </Button>
            <Button 
              onClick={refreshUsage}
              variant="outline"
              className="border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh Usage
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-noir-amber font-semibold">Edge Case Test Results:</h4>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-noir-deep-charcoal rounded border border-noir-amber/10">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(result.status)} mt-1 flex-shrink-0`} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <span className="text-noir-cream font-medium">{result.test}</span>
                        <Badge 
                          variant={result.status === 'success' ? 'default' : result.status === 'warning' ? 'secondary' : 'destructive'} 
                          className="text-xs"
                        >
                          {result.status}
                        </Badge>
                      </div>
                      <p className="text-noir-cream/80 text-sm mt-1">{result.message}</p>
                      {result.data && (
                        <details className="mt-2">
                          <summary className="text-xs text-noir-amber cursor-pointer">View Data</summary>
                          <pre className="text-xs text-noir-cream/60 mt-1 overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AnonymousEdgeCaseTest;