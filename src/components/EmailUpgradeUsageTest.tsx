import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useUsage } from '@/contexts/UsageContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Mail, MessageCircle, CreditCard, ArrowRight, CheckCircle } from 'lucide-react';

interface UsageSnapshot {
  messages_used: number;
  messages_limit: number;
  payg_credits_remaining: number;
  payg_credits_purchased: number;
  subscription_tier: string;
  is_anonymous: boolean;
  timestamp: string;
}

const EmailUpgradeUsageTest: React.FC = () => {
  const { user, isAnonymous, upgradeToEmail } = useAuth();
  const { usageData, refreshUsage } = useUsage();
  const { toast } = useToast();
  const [testEmail, setTestEmail] = useState('');
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [preUpgradeSnapshot, setPreUpgradeSnapshot] = useState<UsageSnapshot | null>(null);
  const [postUpgradeSnapshot, setPostUpgradeSnapshot] = useState<UsageSnapshot | null>(null);
  const [testMessages, setTestMessages] = useState<string[]>([]);

  const takeUsageSnapshot = async (label: string): Promise<UsageSnapshot | null> => {
    try {
      await refreshUsage();
      
      if (!usageData) {
        throw new Error('No usage data available');
      }

      const snapshot: UsageSnapshot = {
        messages_used: usageData.messages_used,
        messages_limit: usageData.messages_limit,
        payg_credits_remaining: usageData.payg_credits_remaining,
        payg_credits_purchased: 0, // We'll get this from the database
        subscription_tier: usageData.tier,
        is_anonymous: usageData.is_anonymous || false,
        timestamp: new Date().toISOString()
      };

      // Get additional data from database
      if (user) {
        const { data: subscription } = await supabase
          .from('user_subscriptions')
          .select('payg_credits_purchased')
          .eq('user_id', user.id)
          .single();

        if (subscription) {
          snapshot.payg_credits_purchased = subscription.payg_credits_purchased;
        }
      }

      console.log(`📊 Usage snapshot (${label}):`, snapshot);
      return snapshot;
    } catch (error) {
      console.error(`Failed to take usage snapshot (${label}):`, error);
      toast({
        title: "Snapshot failed",
        description: `Failed to capture usage data: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
      return null;
    }
  };

  const sendTestMessages = async (count: number = 2): Promise<string[]> => {
    const messages: string[] = [];
    
    try {
      // Get a character for testing
      const { data: characters } = await supabase
        .from('characters')
        .select('id, name')
        .limit(1);

      if (!characters || characters.length === 0) {
        throw new Error('No characters available for testing');
      }

      const character = characters[0];
      
      // Create a test conversation
      const { data: conversation, error: convError } = await supabase
        .from('conversations')
        .insert({
          character_id: character.id,
          character_name: character.name,
          user_id: user!.id,
          title: `Email Upgrade Test - ${new Date().toISOString()}`,
          last_message_preview: 'Test conversation for email upgrade testing',
          message_count: 0,
        })
        .select()
        .single();

      if (convError) throw convError;

      // Send test messages
      for (let i = 1; i <= count; i++) {
        const messageContent = `Test message ${i} before email upgrade - ${new Date().toISOString()}`;
        
        const { error: msgError } = await supabase
          .from('messages')
          .insert({
            conversation_id: conversation.id,
            sender_type: 'user',
            content: messageContent
          });

        if (msgError) throw msgError;
        messages.push(messageContent);
      }

      // Clean up conversation after test
      setTimeout(async () => {
        await supabase.from('messages').delete().eq('conversation_id', conversation.id);
        await supabase.from('conversations').delete().eq('id', conversation.id);
      }, 5000);

      return messages;
    } catch (error) {
      console.error('Failed to send test messages:', error);
      throw error;
    }
  };

  const runEmailUpgradeTest = async () => {
    if (!user || !isAnonymous) {
      toast({
        title: "Invalid test conditions",
        description: "This test requires an anonymous user.",
        variant: "destructive"
      });
      return;
    }

    if (!testEmail) {
      toast({
        title: "Email required",
        description: "Please enter an email address for the upgrade test.",
        variant: "destructive"
      });
      return;
    }

    setIsUpgrading(true);
    
    try {
      // Step 1: Send some test messages to create usage data
      console.log('📝 Sending test messages...');
      const messages = await sendTestMessages(2);
      setTestMessages(messages);
      
      toast({
        title: "Test messages sent",
        description: `Sent ${messages.length} test messages to create usage data.`,
      });

      // Step 2: Take pre-upgrade snapshot
      console.log('📸 Taking pre-upgrade snapshot...');
      const preSnapshot = await takeUsageSnapshot('pre-upgrade');
      if (!preSnapshot) return;
      setPreUpgradeSnapshot(preSnapshot);

      // Step 3: Perform email upgrade
      console.log('📧 Upgrading to email...');
      const { error: upgradeError } = await upgradeToEmail(testEmail);
      
      if (upgradeError) {
        throw upgradeError;
      }

      toast({
        title: "Email upgrade successful",
        description: "Anonymous user successfully upgraded to email account.",
      });

      // Step 4: Wait a moment for data to propagate
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 5: Take post-upgrade snapshot
      console.log('📸 Taking post-upgrade snapshot...');
      const postSnapshot = await takeUsageSnapshot('post-upgrade');
      if (!postSnapshot) return;
      setPostUpgradeSnapshot(postSnapshot);

      // Step 6: Verify data persistence
      const dataPreserved = 
        preSnapshot.messages_used === postSnapshot.messages_used &&
        preSnapshot.payg_credits_remaining === postSnapshot.payg_credits_remaining &&
        preSnapshot.payg_credits_purchased === postSnapshot.payg_credits_purchased &&
        preSnapshot.subscription_tier === postSnapshot.subscription_tier;

      if (dataPreserved) {
        toast({
          title: "✅ Test passed",
          description: "Usage data successfully persisted through email upgrade!",
        });
      } else {
        toast({
          title: "❌ Test failed",
          description: "Usage data was not properly preserved during email upgrade.",
          variant: "destructive"
        });
      }

    } catch (error) {
      console.error('Email upgrade test failed:', error);
      toast({
        title: "Test failed",
        description: `Email upgrade test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive"
      });
    } finally {
      setIsUpgrading(false);
    }
  };

  const resetTest = () => {
    setPreUpgradeSnapshot(null);
    setPostUpgradeSnapshot(null);
    setTestMessages([]);
    setTestEmail('');
  };

  if (!user) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardContent className="p-6">
          <p className="text-noir-cream/80">Please sign in as an anonymous user to run this test.</p>
        </CardContent>
      </Card>
    );
  }

  if (!isAnonymous) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardContent className="p-6">
          <p className="text-noir-cream/80">This test requires an anonymous user. You are already registered with an email.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardHeader>
          <CardTitle className="text-noir-amber flex items-center">
            <Mail className="w-5 h-5 mr-2" />
            Email Upgrade Usage Persistence Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Status */}
          <div className="flex items-center space-x-4">
            <Badge variant="default" className="flex items-center">
              Anonymous User
            </Badge>
            {usageData && (
              <>
                <Badge variant="outline" className="flex items-center">
                  <MessageCircle className="w-3 h-3 mr-1" />
                  {usageData.messages_used}/{usageData.messages_limit} messages
                </Badge>
                {usageData.payg_credits_remaining > 0 && (
                  <Badge variant="outline" className="flex items-center">
                    <CreditCard className="w-3 h-3 mr-1" />
                    {usageData.payg_credits_remaining} PAYG credits
                  </Badge>
                )}
              </>
            )}
          </div>

          {/* Test Email Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-noir-amber">Test Email Address:</label>
            <Input
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="bg-noir-deep-charcoal border-noir-amber/30 text-noir-cream"
              disabled={isUpgrading}
            />
          </div>

          {/* Test Controls */}
          <div className="flex space-x-2">
            <Button 
              onClick={runEmailUpgradeTest}
              disabled={isUpgrading || !testEmail}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              {isUpgrading ? 'Running Test...' : 'Run Email Upgrade Test'}
            </Button>
            <Button 
              onClick={resetTest}
              variant="outline"
              className="border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20"
              disabled={isUpgrading}
            >
              Reset Test
            </Button>
          </div>

          {/* Test Messages */}
          {testMessages.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-noir-amber font-semibold">Test Messages Sent:</h4>
              <div className="space-y-1">
                {testMessages.map((message, index) => (
                  <div key={index} className="text-sm text-noir-cream/80 bg-noir-deep-charcoal p-2 rounded">
                    {message}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Usage Snapshots Comparison */}
          {preUpgradeSnapshot && postUpgradeSnapshot && (
            <div className="space-y-4">
              <h4 className="text-noir-amber font-semibold">Usage Data Comparison:</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Pre-upgrade */}
                <Card className="bg-noir-deep-charcoal border-noir-amber/20">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm text-noir-amber">Before Email Upgrade</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Messages Used:</span>
                      <span className="text-noir-cream">{preUpgradeSnapshot.messages_used}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Message Limit:</span>
                      <span className="text-noir-cream">{preUpgradeSnapshot.messages_limit}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">PAYG Credits:</span>
                      <span className="text-noir-cream">{preUpgradeSnapshot.payg_credits_remaining}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Subscription Tier:</span>
                      <span className="text-noir-cream">{preUpgradeSnapshot.subscription_tier}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Anonymous:</span>
                      <span className="text-noir-cream">{preUpgradeSnapshot.is_anonymous ? 'Yes' : 'No'}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Post-upgrade */}
                <Card className="bg-noir-deep-charcoal border-noir-amber/20">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm text-noir-amber flex items-center">
                      After Email Upgrade
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Messages Used:</span>
                      <div className="flex items-center">
                        <span className="text-noir-cream">{postUpgradeSnapshot.messages_used}</span>
                        {preUpgradeSnapshot.messages_used === postUpgradeSnapshot.messages_used && (
                          <CheckCircle className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Message Limit:</span>
                      <div className="flex items-center">
                        <span className="text-noir-cream">{postUpgradeSnapshot.messages_limit}</span>
                        {postUpgradeSnapshot.messages_limit > preUpgradeSnapshot.messages_limit && (
                          <CheckCircle className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">PAYG Credits:</span>
                      <div className="flex items-center">
                        <span className="text-noir-cream">{postUpgradeSnapshot.payg_credits_remaining}</span>
                        {preUpgradeSnapshot.payg_credits_remaining === postUpgradeSnapshot.payg_credits_remaining && (
                          <CheckCircle className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Subscription Tier:</span>
                      <div className="flex items-center">
                        <span className="text-noir-cream">{postUpgradeSnapshot.subscription_tier}</span>
                        {preUpgradeSnapshot.subscription_tier === postUpgradeSnapshot.subscription_tier && (
                          <CheckCircle className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-noir-cream/80">Anonymous:</span>
                      <div className="flex items-center">
                        <span className="text-noir-cream">{postUpgradeSnapshot.is_anonymous ? 'Yes' : 'No'}</span>
                        {!postUpgradeSnapshot.is_anonymous && (
                          <CheckCircle className="w-3 h-3 text-green-500 ml-1" />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Test Result Summary */}
              <div className="bg-noir-burgundy/20 border border-noir-amber/30 rounded-lg p-4">
                <h5 className="text-noir-amber font-semibold mb-2">Test Results:</h5>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${
                      preUpgradeSnapshot.messages_used === postUpgradeSnapshot.messages_used 
                        ? 'text-green-500' 
                        : 'text-red-500'
                    }`} />
                    <span className="text-noir-cream/90">
                      Message count preserved: {preUpgradeSnapshot.messages_used === postUpgradeSnapshot.messages_used ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${
                      preUpgradeSnapshot.payg_credits_remaining === postUpgradeSnapshot.payg_credits_remaining 
                        ? 'text-green-500' 
                        : 'text-red-500'
                    }`} />
                    <span className="text-noir-cream/90">
                      PAYG credits preserved: {preUpgradeSnapshot.payg_credits_remaining === postUpgradeSnapshot.payg_credits_remaining ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${
                      postUpgradeSnapshot.messages_limit > preUpgradeSnapshot.messages_limit 
                        ? 'text-green-500' 
                        : 'text-yellow-500'
                    }`} />
                    <span className="text-noir-cream/90">
                      Message limit upgraded: {postUpgradeSnapshot.messages_limit > preUpgradeSnapshot.messages_limit ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className={`w-4 h-4 mr-2 ${
                      !postUpgradeSnapshot.is_anonymous 
                        ? 'text-green-500' 
                        : 'text-red-500'
                    }`} />
                    <span className="text-noir-cream/90">
                      Anonymous status updated: {!postUpgradeSnapshot.is_anonymous ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailUpgradeUsageTest;