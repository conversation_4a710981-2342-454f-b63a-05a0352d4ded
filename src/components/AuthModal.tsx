import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import SignInModal from './SignInModal';
import SignUpModal from './SignUpModal';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultMode?: 'signin' | 'signup';
  characterName?: string;
  onOpenWaitlist?: () => void;
}

const AuthModal = ({ isOpen, onClose, defaultMode = 'signin', characterName, onOpenWaitlist }: AuthModalProps) => {
  const [currentMode, setCurrentMode] = useState<'signin' | 'signup'>(defaultMode);
  const [searchParams] = useSearchParams();

  // Check for invite code in URL params
  const inviteCodeParam = searchParams.get('inviteCode');

  useEffect(() => {
    // Set initial mode based on props or URL params
    if (inviteCodeParam) {
      setCurrentMode('signup');
    } else {
      setCurrentMode(defaultMode);
    }
  }, [inviteCodeParam, defaultMode]);

  const handleSwitchToSignUp = () => {
    setCurrentMode('signup');
  };

  const handleSwitchToSignIn = () => {
    setCurrentMode('signin');
  };

  const handleClose = () => {
    setCurrentMode(defaultMode);
    onClose();
  };

  return (
    <>
      <SignInModal
        isOpen={isOpen && currentMode === 'signin'}
        onClose={handleClose}
        characterName={characterName}
        onSwitchToSignUp={handleSwitchToSignUp}
      />
      
      <SignUpModal
        isOpen={isOpen && currentMode === 'signup'}
        onClose={handleClose}
        characterName={characterName}
        onSwitchToSignIn={handleSwitchToSignIn}
        onOpenWaitlist={onOpenWaitlist}
      />
    </>
  );
};

export default AuthModal; 