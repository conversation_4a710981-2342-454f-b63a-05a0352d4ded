import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import ResetPasswordModal from './ResetPasswordModal';

interface SignInModalProps {
  isOpen: boolean;
  onClose: () => void;
  characterName?: string;
  onSwitchToSignUp: () => void;
}

const SignInModal = ({ isOpen, onClose, characterName, onSwitchToSignUp }: SignInModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showResetModal, setShowResetModal] = useState(false);
  const { user, signInWithGoogle } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (user && isOpen) {
      onClose();
      const redirectPath = characterName ? `/?character=${characterName}` : '/';
      navigate(redirectPath);
    }
  }, [user, navigate, characterName, isOpen, onClose]);

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password
      });

      if (error) throw error;

      toast({
        title: "Welcome back!",
        description: "You've been signed in successfully.",
        variant: "default"
      });
    } catch (error: any) {
      console.error('Sign in error:', error);
      
      let errorMessage = "An unexpected error occurred.";
      
      if (error.message?.includes('Invalid login credentials')) {
        errorMessage = "Invalid email or password.";
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Sign In Failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    // setIsLoading(true);
    // try {
    //   const redirectUrl = `${window.location.origin}${characterName ? `/?character=${characterName}` : '/'}`;
      
    //   const { error } = await signInWithGoogle(redirectUrl);
      
    //   if (error) throw error;
    // } catch (error: any) {
    //   console.error('Google auth error:', error);
    //   toast({
    //     title: "Authentication Error",
    //     description: error.message || "Failed to authenticate with Google.",
    //     variant: "destructive"
    //   });
    //   setIsLoading(false);
    // }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const handleForgotPassword = () => {
    setShowResetModal(true);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-md">
          <DialogHeader>
            <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
              Welcome Back
            </DialogTitle>
            <DialogDescription className="text-noir-cream/70 text-center">
              Sign in to continue your screenwriting journey
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <form onSubmit={handleEmailSignIn} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-noir-cream/90">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password" className="text-noir-cream/90">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                  placeholder="Your password"
                  required
                />
              </div>
              
              <Button
                type="submit"
                className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
                disabled={isLoading}
              >
                {isLoading ? 'Please wait...' : 'Sign In'}
              </Button>
            </form>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full border-noir-amber/20" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-noir-charcoal px-2 text-noir-cream/60">Or</span>
              </div>
            </div>

            {/* <Button
              onClick={handleGoogleAuth}
              variant="outline"
              className="w-full bg-noir-shadow border-noir-amber/50 text-noir-cream hover:bg-noir-amber/10 hover:border-noir-amber"
              disabled={isLoading}
            >
              <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              Continue with Google
            </Button> */}

            <div className="text-center space-y-2">
              <Button
                variant="ghost"
                onClick={onSwitchToSignUp}
                className="text-noir-amber hover:text-noir-warm-amber hover:bg-transparent"
                disabled={isLoading}
              >
                Don't have an account? Sign up
              </Button>
              
              <Button
                variant="ghost"
                onClick={handleForgotPassword}
                className="text-noir-cream/60 hover:text-noir-cream hover:bg-transparent text-sm"
                disabled={isLoading}
              >
                Forgot your password?
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <ResetPasswordModal
        isOpen={showResetModal}
        onClose={() => setShowResetModal(false)}
        email={email}
      />
    </>
  );
};

export default SignInModal; 