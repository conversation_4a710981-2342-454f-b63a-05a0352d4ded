import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Mail, Lock } from 'lucide-react';

interface EmailUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  messageCount: number;
  isRequired?: boolean;
  onUpgradeComplete?: () => void;
}

const EmailUpgradeModal: React.FC<EmailUpgradeModalProps> = ({
  isOpen,
  onClose,
  messageCount,
  isRequired = true,
  onUpgradeComplete,
}) => {
  const { upgradeToEmail } = useAuth();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const { error: upgradeError } = await upgradeToEmail(email);
      
      if (upgradeError) {
        setError(upgradeError.message || 'Failed to upgrade account');
        return;
      }

      // Success - close modal
      setEmail('');
      onClose();
      onUpgradeComplete?.();
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!isRequired) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className="sm:max-w-md bg-noir-charcoal border border-noir-amber/20"
        onPointerDownOutside={(e) => {
          if (isRequired) {
            e.preventDefault();
          }
        }}
        onEscapeKeyDown={(e) => {
          if (isRequired) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader className="space-y-4">
          <div className="flex items-center justify-center w-16 h-16 bg-noir-amber/10 rounded-full mx-auto">
            <Mail className="w-8 h-8 text-noir-amber" />
          </div>
          
          <DialogTitle className="text-xl font-source-sans text-noir-cream text-center">
            Reserve Your Permanent Seat
          </DialogTitle>
          
          <DialogDescription className="text-noir-cream/80 text-center">
            You've shared {messageCount} memorable moments at our table. To continue your intimate conversations with Hollywood's greatest minds, please reserve your permanent seat with an email address.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 mt-6">
          {error && (
            <Alert className="border-red-500/20 bg-red-500/10">
              <AlertDescription className="text-red-400">
                {error}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="text-noir-cream font-medium">
              Your Reservation Email
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email to reserve your seat"
              className="bg-noir-charcoal border-noir-amber/30 text-noir-cream placeholder:text-noir-cream/50 focus:border-noir-amber"
              required
              disabled={loading}
              autoComplete="email"
            />
          </div>

          <div className="flex flex-col gap-3 pt-2">
            <Button 
              type="submit" 
              variant="noir-burgundy"
              className="w-full art-deco-button" 
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-noir-charcoal/30 border-t-noir-charcoal rounded-full animate-spin" />
                  Securing Your Reservation...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Lock className="w-4 h-4" />
                  Reserve My Seat
                </div>
              )}
            </Button>

            {!isRequired && (
              <Button
                type="button"
                variant="ghost"
                onClick={onClose}
                className="text-noir-cream/70 hover:text-noir-cream hover:bg-noir-amber/10"
                disabled={loading}
              >
                Maybe Later
              </Button>
            )}
          </div>
        </form>

        <div className="mt-6 p-4 bg-noir-amber/5 rounded-lg border border-noir-amber/20">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-noir-amber rounded-full mt-2 flex-shrink-0" />
            <div className="text-sm text-noir-cream/70">
              <p className="font-medium text-noir-cream mb-1">Your reserved seat includes:</p>
              <ul className="space-y-1 text-xs">
                <li>• Immediate access to continue conversations</li>
                <li>• Your conversation history preserved across devices</li>
                <li>• Account recovery and security features</li>
                <li>• Updates about new dinner guests and features</li>
              </ul>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EmailUpgradeModal; 