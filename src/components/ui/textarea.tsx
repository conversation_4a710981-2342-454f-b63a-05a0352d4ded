import * as React from "react"

import { cn } from "@/lib/utils"

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, ...props }, ref) => {
    return (
      <textarea
        className={cn(
          "flex min-h-[80px] w-full rounded-md border border-noir-input-border bg-noir-input-bg px-3 py-2 text-sm text-noir-input-text ring-offset-background placeholder:text-noir-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-noir-input-focus focus-visible:ring-offset-2 focus-visible:border-noir-input-focus disabled:cursor-not-allowed disabled:opacity-50 font-source-sans transition-all duration-300",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Textarea.displayName = "Textarea"

export { Textarea }
