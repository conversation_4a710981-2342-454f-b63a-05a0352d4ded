import * as React from "react"

import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-noir-input-border bg-noir-input-bg px-3 py-2 text-base text-noir-input-text ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-noir-input-text placeholder:text-noir-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-noir-input-focus focus-visible:ring-offset-2 focus-visible:border-noir-input-focus disabled:cursor-not-allowed disabled:opacity-50 md:text-sm font-source-sans transition-all duration-300",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
