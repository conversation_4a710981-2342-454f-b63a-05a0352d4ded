import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface PasswordSetupModalProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail?: string;
  isRequired?: boolean;
  onSuccess?: () => void;
}

const PasswordSetupModal = ({ isOpen, onClose, userEmail, isRequired = false, onSuccess }: PasswordSetupModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const { toast } = useToast();

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (password !== confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Passwords do not match.",
        variant: "destructive"
      });
      return;
    }

    if (password.length < 6) {
      toast({
        title: "Password Too Short",
        description: "Password must be at least 6 characters long.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase.auth.updateUser({
        password: password,
        data: {
          hasPassword: true
        }
      });

      if (error) {
        throw error;
      }

      setShowSuccess(true);
      // Close modal after a short delay to allow user to see success message
      setTimeout(() => {
        handleClose();
      }, 1000);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error('Password setup error:', error);
      toast({
        title: "Error",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setShowSuccess(false);
    setPassword('');
    setConfirmPassword('');
    onClose();
  };

  // Don't allow closing if required and password hasn't been set successfully
  const canClose = !isRequired || showSuccess;

  if (showSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={canClose ? handleClose : undefined}>
        <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-md">
          <DialogHeader>
            <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
              Welcome to Hollywood Table!
            </DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4 py-6">
            <div className="w-16 h-16 bg-noir-amber rounded-full flex items-center justify-center mx-auto text-2xl">
              ✓
            </div>
            <p className="text-lg text-noir-cream/90">
              Your password has been set successfully!
            </p>
            <p className="text-sm text-noir-cream/70">
              You can now start conversations with any of our legendary screenwriter characters.
            </p>
            <Button
              onClick={() => {
                if (onSuccess) {
                  onSuccess();
                }
                handleClose();
              }}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              Start Exploring
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={canClose ? handleClose : undefined}>
      <DialogContent 
        className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-lg"
        onPointerDownOutside={canClose ? undefined : (e) => e.preventDefault()}
        onEscapeKeyDown={canClose ? undefined : (e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
            {isRequired ? 'Complete Your Account Setup' : 'Set Up Your Password'}
          </DialogTitle>
          <p className="text-center text-noir-cream/70 mt-2">
            {isRequired 
              ? 'To continue using Hollywood Table, please create a secure password for your account.'
              : 'Welcome! Please create a secure password for your account.'
            }
          </p>
        </DialogHeader>

        <div className="space-y-6">
          {userEmail && (
            <div className="text-center">
              <p className="text-sm text-noir-cream/60">
                Setting up password for: <span className="text-noir-amber">{userEmail}</span>
              </p>
            </div>
          )}

          {isRequired && (
            <div className="bg-noir-amber/10 border border-noir-amber/30 rounded-lg p-3">
              <p className="text-noir-amber text-sm font-medium text-center">
                ⚠️ Password setup is required to continue
              </p>
            </div>
          )}

          <form onSubmit={handlePasswordSubmit} className="space-y-4">
            <div>
              <Label htmlFor="password" className="text-noir-cream/90">New Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Enter your password"
                minLength={6}
                required
              />
            </div>

            <div>
              <Label htmlFor="confirmPassword" className="text-noir-cream/90">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Confirm your password"
                minLength={6}
                required
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Setting Up Password...
                </>
              ) : (
                'Set Password'
              )}
            </Button>
          </form>

          <div className="bg-noir-shadow/50 rounded-lg border border-noir-amber/20 p-4">
            <h3 className="text-noir-amber text-sm font-medium mb-2">Password Requirements</h3>
            <ul className="text-noir-cream/70 text-xs space-y-1">
              <li>• At least 6 characters long</li>
              <li>• Mix of letters, numbers, and symbols recommended</li>
              <li>• Choose something secure but memorable</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PasswordSetupModal; 