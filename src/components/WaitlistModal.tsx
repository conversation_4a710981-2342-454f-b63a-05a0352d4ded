
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface WaitlistModalProps {
  isOpen: boolean;
  onClose: () => void;
  characterName?: string;
}

const WaitlistModal = ({ isOpen, onClose, characterName }: WaitlistModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [email, setEmail] = useState('');
  const { toast } = useToast();

  const handleWaitlistSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast({
        title: "Missing Information",
        description: "Please enter your email address.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('waitlist')
        .insert([
          {
            name: '', // We'll only collect email now
            email: email,
            reason: characterName ? `Interested in chatting with ${characterName}` : 'General interest'
          }
        ]);

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          toast({
            title: "Already on Waitlist",
            description: "This email is already on our waitlist. We'll notify you when your invitation is ready!",
            variant: "default"
          });
          // Still send acknowledgment email for existing users
          await sendWaitlistAcknowledgment(email);
        } else {
          throw error;
        }
      } else {
        // Successfully added to waitlist, now send acknowledgment email
        await sendWaitlistAcknowledgment(email);
        setShowSuccess(true);
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sendWaitlistAcknowledgment = async (emailAddress: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      // const apiUrl = import.meta.env.VITE_SUPABASE_URL?.replace('/rest/v1', '') || 'http://localhost:54321';
      
      // const response = await fetch(`${apiUrl}/functions/v1/api/waitlist/send-acknowledgment`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${authToken || ''}`,
      //   },
      //   body: JSON.stringify({ email: emailAddress }),
      // });

      const response = await supabase.functions.invoke('api/waitlist/send-acknowledgment', {
        method: 'POST',
        body: {
          email: emailAddress
        }
      });

      if (response.error) {
        console.warn('Failed to send waitlist acknowledgment email:', response.error);
      } else {
        console.log('Waitlist acknowledgment email sent successfully');
      }
    } catch (error) {
      console.warn('Error sending waitlist acknowledgment email:', error);
      // Don't throw the error - we don't want to prevent the user from seeing success
      // if the waitlist signup worked but the email failed
    }
  };

  const resetModal = () => {
    setShowSuccess(false);
    setEmail('');
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (showSuccess) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-md">
          <DialogHeader>
            <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
              You're on the List!
            </DialogTitle>
          </DialogHeader>
          <div className="text-center space-y-4 py-6">
            <div className="w-16 h-16 bg-noir-amber rounded-full flex items-center justify-center mx-auto text-2xl">
              ✓
            </div>
            <p className="text-lg text-noir-cream/90">
              Thanks! We've sent you a confirmation email and will notify you when you're selected for beta access.
            </p>
            <p className="text-sm text-noir-cream/70">
              Check your inbox for our acknowledgment email, and keep watching for your exclusive invite code to join Hollywood Table.
            </p>
            <Button
              onClick={handleClose}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              Got it
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-lg">
        <DialogHeader>
          <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
            Join the Waitlist
          </DialogTitle>
          <p className="text-center text-noir-cream/70 mt-2">
            Enter your email to receive an exclusive invite code
          </p>
        </DialogHeader>

        <div className="space-y-6">
          <form onSubmit={handleWaitlistSubmit} className="space-y-4">
            <div>
              <Label htmlFor="email" className="text-noir-cream/90">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
            >
              {isLoading ? 'Joining Waitlist...' : 'Join Waitlist'}
            </Button>
          </form>

          {characterName && (
            <div className="text-center pt-4 border-t border-noir-amber/20">
              <p className="text-sm text-noir-cream/60">
                You'll receive an invite code to chat with {characterName} and all other characters.
              </p>
            </div>
          )}

          <div className="bg-noir-shadow/50 rounded-lg border border-noir-amber/20 p-4">
            <h3 className="text-noir-amber text-sm font-medium mb-2">What happens next?</h3>
            <ul className="text-noir-cream/70 text-xs space-y-1">
              <li>• You'll receive an immediate acknowledgment email</li>
              <li>• We'll notify you when you're selected for beta access</li>
              <li>• You'll get an exclusive invite code via email</li>
              <li>• Use the code to create your account and start chatting</li>
            </ul>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WaitlistModal;
