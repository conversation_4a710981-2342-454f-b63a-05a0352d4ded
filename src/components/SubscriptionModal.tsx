
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubscribe: (plan: 'basic' | 'premium') => void;
}

const SubscriptionModal = ({ isOpen, onClose }: SubscriptionModalProps) => {
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleViewPricing = () => {
    onClose();
    navigate('/pricing');
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-noir-deep-charcoal/90 backdrop-blur-sm" onClick={onClose}></div>
      
      <Card className="relative w-full max-w-2xl bg-noir-charcoal border-noir-amber/30 shadow-2xl">
        <div className="p-8 text-center">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <h2 className="font-hollywood text-3xl font-semibold text-noir-amber mb-4">
                Ready to Reserve Your Seat?
              </h2>
              <p className="text-noir-cream/70 text-lg">
                Join Hollywood's greatest screenwriting minds for intimate conversations that will transform your writing.
              </p>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-noir-cream/60 hover:text-noir-amber ml-4"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="bg-noir-burgundy/20 border border-noir-amber/30 rounded-lg p-6 mb-8">
            <div className="text-noir-amber font-medium mb-2">🎬 Limited Time Launch Offer</div>
            <div className="text-2xl font-bold text-noir-cream mb-2">Save up to 33% on all plans</div>
            <div className="text-noir-cream/60">Join 1,000+ screenwriters already learning from the masters</div>
          </div>

          <div className="space-y-4">
            <Button
              onClick={handleViewPricing}
              className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold text-lg py-4"
            >
              View All Pricing Options
            </Button>
            
            <p className="text-noir-cream/50 text-sm">
              Cancel anytime • No hidden fees • 7-day money-back guarantee
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SubscriptionModal;
