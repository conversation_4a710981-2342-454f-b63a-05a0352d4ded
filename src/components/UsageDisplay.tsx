import React from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AlertTriangle, CreditCard, MessageCircle, Users, RefreshCw } from 'lucide-react';
import { useUsage } from '@/contexts/UsageContext';
import PaygCreditsModal from './PaygCreditsModal';

const UsageDisplay = () => {
  const { 
    usageData, 
    loading, 
    error, 
    refreshUsage, 
    lastRefresh 
  } = useUsage();
  
  const [isPaygModalOpen, setIsPaygModalOpen] = React.useState(false);

  const handlePaygPurchaseSuccess = () => {
    console.log('💳 UsageDisplay: PAYG purchase successful, refreshing usage data');
    refreshUsage(); // Refresh usage data after successful purchase
  };

  const handleManualRefresh = () => {
    console.log('🔄 UsageDisplay: Manual refresh triggered');
    refreshUsage();
  };

  if (loading) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20 p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-noir-amber/20 rounded mb-2"></div>
          <div className="h-4 bg-noir-amber/20 rounded w-3/4"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20 p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <p className="text-red-400 text-sm font-medium">Error loading usage data</p>
            <Button
              onClick={handleManualRefresh}
              variant="ghost"
              size="sm"
              className="text-noir-amber hover:text-noir-warm-amber"
            >
              <RefreshCw className="w-3 h-3" />
            </Button>
          </div>
          <p className="text-noir-cream/60 text-xs">{error}</p>
          <Button
            onClick={handleManualRefresh}
            className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber text-xs"
            size="sm"
          >
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  if (!usageData) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20 p-4">
        <div className="space-y-3">
          <p className="text-noir-cream/60 text-sm">Unable to load usage data</p>
          <Button
            onClick={handleManualRefresh}
            className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber text-xs"
            size="sm"
          >
            <RefreshCw className="w-3 h-3 mr-2" />
            Retry
          </Button>
        </div>
      </Card>
    );
  }

  // Use display limit for UI percentage, but actual limit for determining if exceeded
  const displayLimit = usageData.messages_limit;
  const actualLimit = usageData.messages_limit_actual || usageData.messages_limit;
  const messagePercent = Math.round((usageData.messages_used / displayLimit) * 100);
  const messageLimitExceeded = usageData.messages_used >= actualLimit;
  
  const showWarning = messagePercent >= 80;
  const showCritical = messageLimitExceeded;

  return (
    <>
      <Card className={`p-4 transition-all duration-300 ${
        showCritical 
          ? 'bg-red-900/20 border-red-500/50' 
          : showWarning 
          ? 'bg-amber-900/20 border-amber-500/50'
          : 'bg-noir-shadow border-noir-amber/20'
      }`}>
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="w-4 h-4 text-noir-amber" />
              <span className="text-noir-amber font-medium text-sm">
                {usageData.tier}
              </span>
              {usageData.status === 'cancel_at_period_end' && (
                <span className="text-xs bg-amber-900/30 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30">
                  Grace Period
                </span>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {showCritical && (
                <AlertTriangle className="w-4 h-4 text-red-400" />
              )}
              <Button
                onClick={handleManualRefresh}
                variant="ghost"
                size="sm"
                className="text-noir-amber hover:text-noir-warm-amber p-1"
                title="Refresh usage data"
              >
                <RefreshCw className="w-3 h-3" />
              </Button>
            </div>
          </div>

          {/* Usage Bars */}
          <div className="space-y-3">
            {/* Messages */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <span className="text-noir-cream/80 text-xs">Messages</span>
                <span className="text-noir-cream/60 text-xs">
                  {usageData.messages_used} / {displayLimit}
                  {usageData.is_anonymous && (
                    <span className="text-noir-amber/80 ml-1 text-xs"></span>
                  )}
                </span>
              </div>
              <Progress 
                value={messagePercent} 
                className={`h-2 ${
                  messageLimitExceeded 
                    ? '[&>div]:bg-red-500' 
                    : messagePercent >= 80 
                    ? '[&>div]:bg-amber-500' 
                    : '[&>div]:bg-noir-amber'
                }`}
              />
            </div>
          </div>

          {/* Last Update Info */}
          {lastRefresh && (
            <div className="text-xs text-noir-cream/40 text-center">
              Last updated: {lastRefresh.toLocaleTimeString()}
            </div>
          )}

          {/* Pay-as-you-go Credits */}
          {usageData.payg_credits_remaining > 0 && (
            <div className="pt-2 border-t border-noir-amber/10">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CreditCard className="w-3 h-3 text-noir-amber" />
                  <span className="text-noir-cream/80 text-xs">Pay-as-you-go Credits</span>
                </div>
                <span className="text-noir-amber text-xs font-medium">
                  {usageData.payg_credits_remaining}
                </span>
              </div>
            </div>
          )}

          {/* Status Messages and Actions */}
          <div className="space-y-2">
            {usageData.status === 'cancel_at_period_end' && (
              <div className="bg-amber-900/30 border border-amber-500/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-amber-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-amber-300 text-xs font-medium">
                      Subscription Cancelled
                    </p>
                    <p className="text-amber-200/80 text-xs">
                      You'll keep your current usage until your next billing cycle, then switch to free tier limits.
                    </p>
                  </div>
                </div>
              </div>
            )}
            
            {showCritical && (
              <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-red-400 flex-shrink-0 mt-0.5" />
                  <div className="space-y-1">
                    <p className="text-red-300 text-xs font-medium">
                      {usageData.is_anonymous ? 'Email Required to Continue' : 'Message Limit Exceeded'}
                    </p>
                    {usageData.is_anonymous ? (
                      <p className="text-red-200/80 text-xs">
                        You've reached the limit for anonymous users. Provide your email to continue with the free plan ({displayLimit} messages/month).
                      </p>
                    ) : usageData.payg_credits_remaining > 0 ? (
                      <p className="text-red-200/80 text-xs">
                        <MessageCircle className="w-3 h-3 inline mr-1" />
                        Using pay-as-you-go credits for additional messages
                      </p>
                    ) : (
                      <p className="text-red-200/80 text-xs">
                        Purchase credits to continue or upgrade your plan
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {showWarning && !showCritical && (
              <div className="bg-amber-900/30 border border-amber-500/30 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-4 h-4 text-amber-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-amber-300 text-xs font-medium">
                      Approaching Limit
                    </p>
                    <p className="text-amber-200/80 text-xs">
                      Consider purchasing credits or upgrading your plan
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Purchase Credits Button - Hidden for anonymous users */}
            {!usageData.is_anonymous && (
              <Button
                onClick={() => setIsPaygModalOpen(true)}
                className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-medium text-sm"
                size="sm"
              >
                <CreditCard className="w-3 h-3 mr-2" />
                Purchase Credits
              </Button>
            )}
          </div>
        </div>
      </Card>

      <PaygCreditsModal
        isOpen={isPaygModalOpen}
        onClose={() => setIsPaygModalOpen(false)}
        onPurchaseComplete={handlePaygPurchaseSuccess}
      />
    </>
  );
};

export default UsageDisplay; 