import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { useUsage } from '@/contexts/UsageContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { MessageCircle, User, CreditCard, AlertTriangle } from 'lucide-react';

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  data?: any;
}

const AnonymousUsageTest: React.FC = () => {
  const { user, isAnonymous } = useAuth();
  const { usageData, refreshUsage } = useUsage();
  const { toast } = useToast();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result]);
  };

  const runAnonymousUsageTests = async () => {
    if (!user) {
      toast({
        title: "No user found",
        description: "Please sign in to run tests",
        variant: "destructive"
      });
      return;
    }

    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Verify user is anonymous
      addTestResult({
        test: "Anonymous User Detection",
        status: isAnonymous ? 'success' : 'error',
        message: isAnonymous ? 'User correctly identified as anonymous' : 'User not detected as anonymous',
        data: { isAnonymous, hasEmail: !!user.email, userMetadata: user.user_metadata }
      });

      // Test 2: Check initial usage data
      await refreshUsage();
      addTestResult({
        test: "Usage Data Retrieval",
        status: usageData ? 'success' : 'error',
        message: usageData ? 'Usage data retrieved successfully' : 'Failed to retrieve usage data',
        data: usageData
      });

      // Test 3: Verify anonymous user limits
      if (usageData) {
        const expectedLimit = isAnonymous ? 3 : usageData.messages_limit;
        const actualLimit = usageData.messages_limit;
        
        addTestResult({
          test: "Anonymous User Limit Enforcement",
          status: actualLimit === expectedLimit ? 'success' : 'error',
          message: `Expected limit: ${expectedLimit}, Actual limit: ${actualLimit}`,
          data: { expectedLimit, actualLimit, isAnonymous }
        });
      }

      // Test 4: Test message counting
      const testConversationId = await createTestConversation();
      if (testConversationId) {
        for (let i = 1; i <= 4; i++) {
          const messageResult = await sendTestMessage(testConversationId, i);
          addTestResult({
            test: `Message ${i} Sending`,
            status: messageResult.success ? 'success' : 'error',
            message: messageResult.message,
            data: messageResult.data
          });

          // Refresh usage after each message
          await refreshUsage();
          
          // Check if anonymous user is blocked after 3 messages
          if (isAnonymous && i >= 3) {
            const canSendMore = await checkCanSendMessage();
            addTestResult({
              test: `Anonymous Limit Check (after ${i} messages)`,
              status: !canSendMore ? 'success' : 'error',
              message: !canSendMore ? 'Anonymous user correctly blocked' : 'Anonymous user should be blocked but can still send',
              data: { messageCount: i, canSendMore, usageData }
            });
          }
        }

        // Cleanup test conversation
        await cleanupTestConversation(testConversationId);
      }

      // Test 5: Test PAYG credit functionality for anonymous users
      if (isAnonymous) {
        const paygTest = await testPaygCreditsForAnonymous();
        addTestResult({
          test: "PAYG Credits for Anonymous Users",
          status: paygTest.success ? 'success' : 'error',
          message: paygTest.message,
          data: paygTest.data
        });
      }

    } catch (error) {
      addTestResult({
        test: "Test Execution",
        status: 'error',
        message: `Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      });
    } finally {
      setIsRunning(false);
    }
  };

  const createTestConversation = async (): Promise<string | null> => {
    try {
      // Get a character for testing
      const { data: characters } = await supabase
        .from('characters')
        .select('id, name')
        .limit(1);

      if (!characters || characters.length === 0) {
        throw new Error('No characters available for testing');
      }

      const character = characters[0];
      const { data: conversation, error } = await supabase
        .from('conversations')
        .insert({
          character_id: character.id,
          character_name: character.name,
          user_id: user!.id,
          title: `Test Chat - ${new Date().toISOString()}`,
          last_message_preview: 'Test conversation for usage testing',
          message_count: 0,
        })
        .select()
        .single();

      if (error) throw error;
      return conversation.id;
    } catch (error) {
      console.error('Failed to create test conversation:', error);
      return null;
    }
  };

  const sendTestMessage = async (conversationId: string, messageNumber: number) => {
    try {
      const { error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_type: 'user',
          content: `Test message ${messageNumber} - ${new Date().toISOString()}`
        });

      if (error) throw error;

      return {
        success: true,
        message: `Message ${messageNumber} sent successfully`,
        data: { messageNumber, conversationId }
      };
    } catch (error) {
      return {
        success: false,
        message: `Failed to send message ${messageNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { messageNumber, error }
      };
    }
  };

  const checkCanSendMessage = async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.functions.invoke('api/usage/check-limits', {
        method: 'POST',
        body: { action_type: 'message' }
      });

      if (error) throw error;
      return data?.can_perform || false;
    } catch (error) {
      console.error('Failed to check message limits:', error);
      return false;
    }
  };

  const testPaygCreditsForAnonymous = async () => {
    try {
      // Anonymous users should not be able to use PAYG credits
      // They should be prompted to upgrade to email instead
      const { data, error } = await supabase.functions.invoke('api/usage/check-limits', {
        method: 'POST',
        body: { action_type: 'message' }
      });

      if (error) throw error;

      const shouldBeBlocked = !data.can_perform && data.reason?.includes('Anonymous user message limit reached');
      
      return {
        success: shouldBeBlocked,
        message: shouldBeBlocked 
          ? 'Anonymous user correctly blocked and prompted for email upgrade'
          : 'Anonymous user should be blocked but was not',
        data: { canPerform: data.can_perform, reason: data.reason }
      };
    } catch (error) {
      return {
        success: false,
        message: `PAYG test failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        data: { error }
      };
    }
  };

  const cleanupTestConversation = async (conversationId: string) => {
    try {
      // Delete messages first
      await supabase
        .from('messages')
        .delete()
        .eq('conversation_id', conversationId);

      // Delete conversation
      await supabase
        .from('conversations')
        .delete()
        .eq('id', conversationId);
    } catch (error) {
      console.error('Failed to cleanup test conversation:', error);
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      case 'pending': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  if (!user) {
    return (
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardContent className="p-6">
          <p className="text-noir-cream/80">Please sign in to run anonymous usage tests.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="bg-noir-shadow border-noir-amber/20">
        <CardHeader>
          <CardTitle className="text-noir-amber flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Anonymous Usage Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* User Status */}
          <div className="flex items-center space-x-4">
            <Badge variant={isAnonymous ? "default" : "secondary"} className="flex items-center">
              <User className="w-3 h-3 mr-1" />
              {isAnonymous ? 'Anonymous User' : 'Registered User'}
            </Badge>
            {usageData && (
              <Badge variant="outline" className="flex items-center">
                <MessageCircle className="w-3 h-3 mr-1" />
                {usageData.messages_used}/{usageData.messages_limit} messages
              </Badge>
            )}
            {usageData?.payg_credits_remaining > 0 && (
              <Badge variant="outline" className="flex items-center">
                <CreditCard className="w-3 h-3 mr-1" />
                {usageData.payg_credits_remaining} PAYG credits
              </Badge>
            )}
          </div>

          {/* Test Controls */}
          <div className="flex space-x-2">
            <Button 
              onClick={runAnonymousUsageTests}
              disabled={isRunning}
              className="bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
            >
              {isRunning ? 'Running Tests...' : 'Run Usage Tests'}
            </Button>
            <Button 
              onClick={refreshUsage}
              variant="outline"
              className="border-noir-amber/50 text-noir-amber hover:bg-noir-amber/20"
            >
              Refresh Usage Data
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-noir-amber font-semibold">Test Results:</h4>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-noir-deep-charcoal rounded border border-noir-amber/10">
                    <div className={`w-3 h-3 rounded-full ${getStatusColor(result.status)} mt-1 flex-shrink-0`} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <span className="text-noir-cream font-medium">{result.test}</span>
                        <Badge variant={result.status === 'success' ? 'default' : 'destructive'} className="text-xs">
                          {result.status}
                        </Badge>
                      </div>
                      <p className="text-noir-cream/80 text-sm mt-1">{result.message}</p>
                      {result.data && (
                        <details className="mt-2">
                          <summary className="text-xs text-noir-amber cursor-pointer">View Data</summary>
                          <pre className="text-xs text-noir-cream/60 mt-1 overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AnonymousUsageTest;