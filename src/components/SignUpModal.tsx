import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface SignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  characterName?: string;
  onSwitchToSignIn: () => void;
  onOpenWaitlist?: () => void;
}

const SignUpModal = ({ isOpen, onClose, characterName, onSwitchToSignIn, onOpenWaitlist }: SignUpModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const { user, signInWithGoogle } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Check for invite code and character params
  const inviteCodeParam = searchParams.get('inviteCode');
  const characterParam = searchParams.get('character') || characterName;

  useEffect(() => {
    if (user && isOpen) {
      onClose();
      const redirectPath = characterParam ? `/?character=${characterParam}` : '/';
      navigate(redirectPath);
    }
  }, [user, navigate, characterParam, isOpen, onClose]);

  useEffect(() => {
    if (inviteCodeParam) {
      setInviteCode(inviteCodeParam);
    }
  }, [inviteCodeParam]);

  const validateInviteCode = async (code: string) => {
    try {
      const { data, error } = await supabase.rpc('use_invite_code', {
        invite_code: code.trim().toUpperCase()
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Invite code validation error:', error);
      return false;
    }
  };

  const handleEmailSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!inviteCode.trim()) {
      toast({
        title: "Invite Code Required",
        description: "Please enter your invite code to sign up.",
        variant: "destructive"
      });
      return;
    }

    if (password !== confirmPassword) {
      toast({
        title: "Password Mismatch",
        description: "Passwords do not match.",
        variant: "destructive"
      });
      return;
    }

    // Validate invite code first
    const isValidCode = await validateInviteCode(inviteCode);
    if (!isValidCode) {
      toast({
        title: "Invalid Invite Code",
        description: "This invite code is invalid, expired, or already used.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const redirectUrl = `${window.location.origin}/`;
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: redirectUrl,
          data: {
            full_name: fullName,
            hasPassword: true
          }
        }
      });

      if (error) throw error;

      toast({
        title: "Check your email",
        description: "We've sent you a confirmation link to complete your registration.",
        variant: "default"
      });

      // Close the modal after successful signup
      handleClose();
    } catch (error: any) {
      console.error('Sign up error:', error);
      
      let errorMessage = "An unexpected error occurred.";
      
      if (error.message?.includes('User already registered')) {
        errorMessage = "An account with this email already exists.";
      } else if (error.message?.includes('Password should be at least 6 characters')) {
        errorMessage = "Password should be at least 6 characters long.";
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      toast({
        title: "Sign Up Failed",
        description: errorMessage,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    // setIsLoading(true);
    // try {
    //   const redirectUrl = `${window.location.origin}${characterParam ? `/?character=${characterParam}` : '/'}`;
      
    //   const { error } = await signInWithGoogle(redirectUrl);
      
    //   if (error) throw error;
    // } catch (error: any) {
    //   console.error('Google auth error:', error);
    //   toast({
    //     title: "Authentication Error",
    //     description: error.message || "Failed to authenticate with Google.",
    //     variant: "destructive"
    //   });
    //   setIsLoading(false);
    // }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setConfirmPassword('');
    setFullName('');
    setInviteCode('');
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-noir-charcoal border-noir-amber/30 text-noir-cream max-w-md">
        <DialogHeader>
          <DialogTitle className="font-hollywood text-2xl text-noir-amber text-center">
            Join Hollywood Table
          </DialogTitle>
          <DialogDescription className="text-noir-cream/70 text-center">
            Create your account with your invite code
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <form onSubmit={handleEmailSignUp} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="inviteCode" className="text-noir-cream/90">Invite Code *</Label>
              <Input
                id="inviteCode"
                type="text"
                value={inviteCode}
                onChange={(e) => setInviteCode(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Enter your invite code"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="fullName" className="text-noir-cream/90">Full Name</Label>
              <Input
                id="fullName"
                type="text"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Your full name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email" className="text-noir-cream/90">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="<EMAIL>"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password" className="text-noir-cream/90">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Your password"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-noir-cream/90">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-noir-shadow border-noir-amber/30 text-noir-cream focus:border-noir-amber"
                placeholder="Confirm your password"
                required
              />
            </div>
            
            <Button
              type="submit"
              className="w-full bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
              disabled={isLoading}
            >
              {isLoading ? 'Please wait...' : 'Create Account'}
            </Button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <Separator className="w-full border-noir-amber/20" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-noir-charcoal px-2 text-noir-cream/60">Or</span>
            </div>
          </div>

          {/* <Button
            onClick={handleGoogleAuth}
            variant="outline"
            className="w-full bg-noir-shadow border-noir-amber/50 text-noir-cream hover:bg-noir-amber/10 hover:border-noir-amber"
            disabled={isLoading}
          >
            <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
              <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
          </Button> */}

          <div className="text-center space-y-2">
            <Button
              variant="ghost"
              onClick={onSwitchToSignIn}
              className="text-noir-amber hover:text-noir-warm-amber hover:bg-transparent"
              disabled={isLoading}
            >
              Already have an account? Sign in
            </Button>
          </div>

          <div className="mt-6 p-4 bg-noir-shadow/50 rounded-lg border border-noir-amber/20">
            <h3 className="text-noir-amber text-sm font-medium mb-2">Need an invite code?</h3>
            <p className="text-noir-cream/70 text-xs mb-2">
              Join our waitlist to receive an exclusive invite code via email.
            </p>
            {onOpenWaitlist && (
              <Button
                onClick={() => {
                  onClose();
                  onOpenWaitlist();
                }}
                variant="ghost"
                className="text-noir-amber hover:text-noir-warm-amber text-xs p-0 h-auto font-normal"
              >
                Join the waitlist →
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SignUpModal; 