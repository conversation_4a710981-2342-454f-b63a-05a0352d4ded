// Frontend pricing configuration - display information only
// Connects to backend pricing via tier names

export interface PaygPackageDisplay {
  id: string;
  name: string;
  description: string;
  credits: number;
  price_dollars: number; // Display price in dollars
  popular?: boolean;
  savings?: string;
}

// PAYG Package Display Configuration
export const PAYG_PACKAGES_DISPLAY: PaygPackageDisplay[] = [
  {
    id: 'starter',
    name: "Starter",
    description: "25 additional message credits",
    credits: 25,
    price_dollars: 4.99
  },
  {
    id: 'writers',
    name: "Writer's",
    description: "75 additional message credits",
    credits: 75,
    price_dollars: 9.99,
    popular: true,
    savings: "Best Value"
  },
  {
    id: 'pro',
    name: "Pro",
    description: "200 additional message credits",
    credits: 200,
    price_dollars: 19.99,
    savings: "Most Credits"
  }
];

// Subscription Tier Display Configuration
export interface SubscriptionTierDisplay {
  name: string;
  tier: string;
  monthlyPrice: string;
  annualPrice: string;
  annualSavings: string;
  originalPrice?: string;
  description: string;
  features: string[];
  limitations: string[];
  cta: string;
  popular: boolean;
  badge: string;
  buttonClass: string;
}

export const SUBSCRIPTION_TIERS_DISPLAY: SubscriptionTierDisplay[] = [
  {
    name: "Sample the Table",
    tier: "FREE TIER",
    monthlyPrice: "Free",
    annualPrice: "Free",
    annualSavings: "",
    description: "Perfect for getting a taste of Hollywood wisdom",
    features: [
      "10 messages per month",
      "Choose from Max Sterling, Danny Cross, or other available characters",
      "Pay-as-you-go available for additional messages",
      "Basic chat interface"
    ],
    limitations: [],
    cta: "Start Free",
    popular: false,
    badge: "GET STARTED",
    buttonClass: "bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber"
  },
  {
    name: "Reserved Seat",
    tier: "FOUNDER PRICING",
    monthlyPrice: "$14.99",
    annualPrice: "$149.99",
    annualSavings: "save $30",
    originalPrice: "$19.99",
    description: "For serious screenwriters ready to learn from the masters",
    features: [
      "100 conversations per month",
      "Pay-as-you-go available for additional messages",
      "Full conversation history saved",
      "All current and future characters included",
      "Priority character responses"
    ],
    limitations: [],
    cta: "Claim Founder Price",
    popular: true,
    badge: "MOST POPULAR",
    buttonClass: "bg-noir-amber text-noir-charcoal hover:bg-noir-warm-amber font-semibold"
  },
  {
    name: "VIP Table",
    tier: "PREMIUM PLAN",
    monthlyPrice: "$34.99",
    annualPrice: "$349.99",
    annualSavings: "save $70",
    description: "Best value for dedicated screenwriting professionals",
    features: [
      "Unlimited messages with ALL characters",
      "Everything in Reserved Seat",
      "Exclusive Master Class conversations",
      "Priority support",
      "Early access to new characters",
      "Export conversation transcripts"
    ],
    limitations: [],
    cta: "Get VIP Access",
    popular: false,
    badge: "UNLIMITED",
    buttonClass: "bg-noir-burgundy text-noir-cream hover:bg-noir-deep-burgundy font-semibold border border-noir-amber"
  }
];

// Tier hierarchy for upgrade/downgrade logic
export const TIER_HIERARCHY = ["Sample the Table", "Reserved Seat", "VIP Table"];

// Helper function to get tier index for comparison
export const getTierIndex = (tierName: string): number => {
  return TIER_HIERARCHY.indexOf(tierName);
};

// Helper function to determine if a tier change is an upgrade
export const isUpgrade = (currentTier: string, targetTier: string): boolean => {
  const currentIndex = getTierIndex(currentTier);
  const targetIndex = getTierIndex(targetTier);
  return targetIndex > currentIndex;
}; 