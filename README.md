# Hollywood Table

A sophisticated platform for engaging conversations with AI personas of legendary screenwriters.

## Features

### Authentication
- **Invite-only Beta Access**: Users can join via invitation codes
- **Email Invitation System**: Admin can send invitation emails via Supabase Edge Functions
- **Password Setup Modal**: When users log in via invitation link, they're prompted to set up their password
- **Waitlist System**: Users can join a waitlist to receive invitation codes

### Password Setup Flow

When users receive an invitation email and click the verification link, they are automatically logged in and redirected to the platform. The system now uses user metadata to determine if password setup is required:

- When admin sends invitations via Edge Functions, new users are created with `hasPassword: false` in their metadata
- Upon login, the platform checks `user.user_metadata.hasPassword === false` and automatically shows a password setup modal
- After the user successfully sets their password, the metadata is updated to `hasPassword: true` and the modal is closed
- This approach is more reliable than URL parameters and works regardless of redirect paths

### Character System
- Multiple AI screenwriter personas inspired by Hollywood legends
- Character cards with detailed backgrounds and signatures
- Interactive conversations with context-aware responses

## Setup

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Configure Supabase environment variables

## Tech Stack

- React + TypeScript
- Vite
- Tailwind CSS
- Supabase (Auth, Database, Edge Functions)
- React Router
- Shadcn/ui Components

## Project info

**URL**: https://lovable.dev/projects/10b37bb5-8e1c-4d3a-9f8f-92a35ce1c70e

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/10b37bb5-8e1c-4d3a-9f8f-92a35ce1c70e) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/10b37bb5-8e1c-4d3a-9f8f-92a35ce1c70e) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
