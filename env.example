# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Stripe Configuration
# Get these from your Stripe Dashboard (https://dashboard.stripe.com/apikeys)
# Environment-specific keys (recommended):
STRIPE_TEST_SECRET_KEY=sk_test_your_stripe_test_secret_key_here
STRIPE_LIVE_SECRET_KEY=sk_live_your_stripe_live_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_TEST_PUBLISHABLE_KEY
STRIPE_TEST_SECRET_KEY
STRIPE_LIVE_PUBLISHABLE_KEY
STRIPE_LIVE_SECRET_KEY

# Stripe Webhook Configuration
# Create webhook endpoints in Stripe Dashboard and get the signing secrets

# For Pay-as-you-go credits webhook
# Webhook should point to: https://your-project-id.supabase.co/functions/v1/stripe-webhook-payg
# Required events: payment_intent.succeeded
STRIPE_WEBHOOK_SECRET=whsec_your_payg_webhook_signing_secret_here

# For Subscription webhook
# Webhook should point to: https://your-project-id.supabase.co/functions/v1/stripe-webhook-subscription
# Required events: checkout.session.completed, customer.subscription.created, customer.subscription.updated, customer.subscription.deleted, invoice.payment_failed, invoice.payment_succeeded
STRIPE_WEBHOOK_SECRET_SUBSCRIPTION=whsec_your_subscription_webhook_signing_secret_here

# Stripe Price IDs for Subscription Tiers
# Create these products/prices in your Stripe Dashboard
# Reserved Seat Tier
STRIPE_PRICE_ID_RESERVED_SEAT_MONTHLY=price_reserved_seat_monthly_id
STRIPE_PRICE_ID_RESERVED_SEAT_ANNUAL=price_reserved_seat_annual_id

# VIP Table Tier  
STRIPE_PRICE_ID_VIP_TABLE_MONTHLY=price_vip_table_monthly_id
STRIPE_PRICE_ID_VIP_TABLE_ANNUAL=price_vip_table_annual_id

# Application Configuration
VITE_SUPABASE_URL=${SUPABASE_URL}
VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
VITE_STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}

# Development/Production Environment
NODE_ENV=dev or production

# Optional: Custom Domain Configuration
# CUSTOM_DOMAIN=yourdomain.com

# Notes:
# 1. Replace all placeholder values with actual keys from your Stripe Dashboard
# 2. Ensure webhook endpoint is configured in Stripe to send events to your Supabase function
# 3. Required webhook events: payment_intent.succeeded
# 4. Test keys start with sk_test_ and pk_test_, live keys start with sk_live_ and pk_live_
# 5. Keep your secret keys secure and never commit them to version control 