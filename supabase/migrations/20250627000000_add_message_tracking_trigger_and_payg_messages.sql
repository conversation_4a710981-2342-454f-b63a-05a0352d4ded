-- Migration to add message tracking trigger and payg_messages table
-- This ensures conversation.message_count and user_subscription.messages_used_this_period are automatically updated
-- Also adds proper tracking for pay-as-you-go message usage

-- Create payg_messages table to track which messages were paid for with PAYG credits
CREATE TABLE public.payg_messages (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  message_id UUID REFERENCES public.messages(id) ON DELETE CASCADE NOT NULL,
  credits_used INTEGER NOT NULL DEFAULT 1,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  -- Prevent duplicate tracking of the same message
  UNIQUE(message_id)
);

-- Enable RLS on payg_messages table
ALTER TABLE public.payg_messages ENABLE ROW LEVEL SECURITY;

-- Create policies for payg_messages table
CREATE POLICY "Users can view their own payg messages" 
  ON public.payg_messages 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all payg messages" 
  ON public.payg_messages 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_payg_messages_user_id ON public.payg_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_payg_messages_message_id ON public.payg_messages(message_id);
CREATE INDEX IF NOT EXISTS idx_payg_messages_created_at ON public.payg_messages(created_at);

-- Create comprehensive message tracking trigger function
CREATE OR REPLACE FUNCTION track_message_usage()
RETURNS TRIGGER AS $$
DECLARE
  conv_user_id UUID;
  subscription_record RECORD;
  within_limits BOOLEAN;
  current_user_messages INTEGER;
BEGIN
  -- Only track user messages (not AI responses)
  IF NEW.sender_type != 'user' THEN
    -- Still update conversation metadata for all messages
    UPDATE public.conversations 
    SET 
      message_count = (
        SELECT COUNT(*) FROM public.messages 
        WHERE conversation_id = NEW.conversation_id
      ),
      last_message_preview = LEFT(NEW.content, 60),
      updated_at = NOW()
    WHERE id = NEW.conversation_id;
    
    RETURN NEW;
  END IF;

  -- Get the user_id from the conversation
  SELECT user_id INTO conv_user_id 
  FROM public.conversations 
  WHERE id = NEW.conversation_id;

  IF conv_user_id IS NULL THEN
    RAISE EXCEPTION 'Could not find user_id for conversation: %', NEW.conversation_id;
  END IF;

  -- Get user's subscription information
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = conv_user_id;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', conv_user_id;
  END IF;

  -- Count current user messages in this period (including the new one)
  SELECT COUNT(m.id) INTO current_user_messages
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = conv_user_id
    AND m.sender_type = 'user'
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Determine if this message is within subscription limits
  within_limits := current_user_messages <= subscription_record.monthly_message_limit;

  -- If over limits and using PAYG credits, track in payg_messages table
  IF NOT within_limits AND subscription_record.payg_credits_remaining > 0 THEN
    -- Insert into payg_messages table
    INSERT INTO public.payg_messages (user_id, message_id, credits_used)
    VALUES (conv_user_id, NEW.id, 1);
    
    -- Update subscription: increment payg_credits_used and decrement remaining
    UPDATE public.user_subscriptions 
    SET 
      payg_credits_used = payg_credits_used + 1,
      payg_credits_remaining = payg_credits_remaining - 1,
      messages_used_this_period = current_user_messages,
      updated_at = NOW()
    WHERE user_id = conv_user_id;
  ELSE
    -- Within limits or no PAYG credits - just update message count
    UPDATE public.user_subscriptions 
    SET 
      messages_used_this_period = current_user_messages,
      updated_at = NOW()
    WHERE user_id = conv_user_id;
  END IF;

  -- Update conversation metadata (for all messages)
  UPDATE public.conversations 
  SET 
    message_count = (
      SELECT COUNT(*) FROM public.messages 
      WHERE conversation_id = NEW.conversation_id
    ),
    last_message_preview = LEFT(NEW.content, 60),
    updated_at = NOW()
  WHERE id = NEW.conversation_id;

  -- Log for debugging
  RAISE NOTICE 'track_message_usage: user=%, message=%, within_limits=%, total_user_messages=%, payg_remaining=%', 
    conv_user_id, 
    NEW.id, 
    within_limits, 
    current_user_messages, 
    subscription_record.payg_credits_remaining;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop existing trigger if it exists and create new comprehensive one
DROP TRIGGER IF EXISTS update_conversation_on_message_insert ON public.messages;
DROP TRIGGER IF EXISTS track_message_usage_trigger ON public.messages;

-- Create new trigger that handles both conversation updates and usage tracking
CREATE TRIGGER track_message_usage_trigger
  AFTER INSERT ON public.messages
  FOR EACH ROW
  EXECUTE FUNCTION track_message_usage();

-- Function to retroactively populate payg_messages for existing data (backward compatibility)
CREATE OR REPLACE FUNCTION backfill_payg_messages()
RETURNS TABLE(
  processed_users INTEGER,
  processed_messages INTEGER,
  payg_messages_created INTEGER
) AS $$
DECLARE
  user_record RECORD;
  message_record RECORD;
  subscription_record RECORD;
  user_message_count INTEGER;
  messages_over_limit INTEGER;
  payg_count INTEGER := 0;
  total_users INTEGER := 0;
  total_messages INTEGER := 0;
BEGIN
  -- Loop through all users with subscriptions
  FOR user_record IN 
    SELECT DISTINCT user_id FROM public.user_subscriptions
  LOOP
    total_users := total_users + 1;
    
    -- Get user's subscription
    SELECT * INTO subscription_record 
    FROM public.user_subscriptions 
    WHERE user_id = user_record.user_id;
    
    -- Count user messages in current period
    SELECT COUNT(m.id) INTO user_message_count
    FROM public.messages m
    JOIN public.conversations c ON m.conversation_id = c.id
    WHERE c.user_id = user_record.user_id
      AND m.sender_type = 'user'
      AND m.created_at >= subscription_record.current_period_start
      AND m.created_at <= subscription_record.current_period_end;
    
    total_messages := total_messages + user_message_count;
    
    -- If user has messages over their limit, find which ones should be PAYG
    IF user_message_count > subscription_record.monthly_message_limit THEN
      messages_over_limit := user_message_count - subscription_record.monthly_message_limit;
      
      -- Find the messages that would have been over the limit (chronologically)
      FOR message_record IN
        SELECT m.id as message_id
        FROM public.messages m
        JOIN public.conversations c ON m.conversation_id = c.id
        WHERE c.user_id = user_record.user_id
          AND m.sender_type = 'user'
          AND m.created_at >= subscription_record.current_period_start
          AND m.created_at <= subscription_record.current_period_end
        ORDER BY m.created_at ASC
        OFFSET subscription_record.monthly_message_limit
        LIMIT messages_over_limit
      LOOP
        -- Insert into payg_messages if not already exists
        INSERT INTO public.payg_messages (user_id, message_id, credits_used)
        VALUES (user_record.user_id, message_record.message_id, 1)
        ON CONFLICT (message_id) DO NOTHING;
        
        -- Count successful inserts
        IF FOUND THEN
          payg_count := payg_count + 1;
        END IF;
      END LOOP;
    END IF;
  END LOOP;
  
  RETURN QUERY SELECT total_users, total_messages, payg_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to recalculate PAYG credits used based on payg_messages table
CREATE OR REPLACE FUNCTION recalculate_payg_credits_used()
RETURNS VOID AS $$
BEGIN
  -- Update payg_credits_used based on actual payg_messages entries
  UPDATE public.user_subscriptions 
  SET 
    payg_credits_used = COALESCE((
      SELECT SUM(pm.credits_used)
      FROM public.payg_messages pm
      WHERE pm.user_id = user_subscriptions.user_id
    ), 0),
    payg_credits_remaining = payg_credits_purchased - COALESCE((
      SELECT SUM(pm.credits_used)
      FROM public.payg_messages pm
      WHERE pm.user_id = user_subscriptions.user_id
    ), 0),
    updated_at = NOW();
    
  RAISE NOTICE 'Recalculated PAYG credits for all users based on payg_messages table';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up payg_messages for deleted messages (maintain referential integrity)
CREATE OR REPLACE FUNCTION cleanup_orphaned_payg_messages()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete payg_messages entries where the referenced message no longer exists
  WITH deleted AS (
    DELETE FROM public.payg_messages 
    WHERE message_id NOT IN (
      SELECT id FROM public.messages
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update can_user_perform_action to count messages directly instead of using cached value
DROP FUNCTION IF EXISTS can_user_perform_action(UUID, TEXT);

CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- only 'message' is supported now
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  limit_value INTEGER;
  within_limits BOOLEAN;
BEGIN
  -- Only support 'message' action type now
  IF action_type != 'message' THEN
    RETURN QUERY SELECT FALSE, 'Only message action type is supported'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Get subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Count current user messages directly from messages table (real-time count)
  SELECT COUNT(m.id) INTO current_usage
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.sender_type = 'user'  -- Only count user messages, NOT AI responses
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Check current usage against limits
  limit_value := subscription_record.monthly_message_limit;
  within_limits := current_usage < limit_value;

  -- Prepare subscription info JSON with real-time usage count
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'messages_used', current_usage,  -- Use real-time count, not cached value
      'messages_limit', subscription_record.monthly_message_limit,
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end
    );

    -- Log for debugging
    RAISE NOTICE 'can_user_perform_action: user_id=%, real_time_usage=%/%, within_limits=%, payg_credits=%', 
      user_uuid, 
      current_usage, 
      limit_value, 
      within_limits, 
      subscription_record.payg_credits_remaining;

    -- If within limits, allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over limits and no credits
    RETURN QUERY SELECT FALSE, 'Message limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION track_message_usage() TO authenticated;
GRANT EXECUTE ON FUNCTION backfill_payg_messages() TO authenticated;
GRANT EXECUTE ON FUNCTION recalculate_payg_credits_used() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_orphaned_payg_messages() TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_perform_action(UUID, TEXT) TO authenticated;

-- Create a view for easy PAYG usage analysis
CREATE OR REPLACE VIEW payg_usage_summary AS
SELECT 
  us.user_id,
  p.email,
  us.subscription_tier,
  us.payg_credits_purchased,
  us.payg_credits_used as subscription_payg_used,
  COALESCE(pm_actual.actual_payg_used, 0) as actual_payg_used,
  us.payg_credits_remaining,
  us.messages_used_this_period,
  us.monthly_message_limit,
  CASE 
    WHEN us.messages_used_this_period > us.monthly_message_limit 
    THEN us.messages_used_this_period - us.monthly_message_limit 
    ELSE 0 
  END as messages_over_limit
FROM public.user_subscriptions us
LEFT JOIN public.profiles p ON us.user_id = p.id
LEFT JOIN (
  SELECT 
    user_id, 
    SUM(credits_used) as actual_payg_used 
  FROM public.payg_messages 
  GROUP BY user_id
) pm_actual ON us.user_id = pm_actual.user_id;

-- Add comment explaining the migration
COMMENT ON TABLE public.payg_messages IS 'Tracks individual messages that were paid for using pay-as-you-go credits. Provides granular tracking for billing and usage analysis.';
COMMENT ON FUNCTION track_message_usage() IS 'Trigger function that automatically updates conversation message counts and user subscription usage when new messages are added. Handles PAYG credit usage tracking.';
COMMENT ON FUNCTION backfill_payg_messages() IS 'One-time function to populate payg_messages table with existing data for backward compatibility.';
COMMENT ON VIEW payg_usage_summary IS 'Summary view showing PAYG credit usage comparison between subscription table and detailed message tracking.'; 