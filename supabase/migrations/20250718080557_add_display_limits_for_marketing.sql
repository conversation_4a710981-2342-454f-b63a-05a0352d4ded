-- Migration to add display limits for marketing purposes
-- This allows showing users a "10-message limit on free plan" while internally enforcing actual limits

-- Update the can_user_perform_action function to support display limits
CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- only 'message' is supported now
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  enforcement_limit INTEGER;
  display_limit INTEGER;
  within_limits BOOLEAN;
  is_anon BOOLEAN;
BEGIN
  -- Only support 'message' action type now
  IF action_type != 'message' THEN
    RETURN QUERY SELECT FALSE, 'Only message action type is supported'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Check if user is anonymous
  is_anon := is_anonymous_user(user_uuid);

  -- Get subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Count current user messages directly from messages table (real-time count)
  SELECT COUNT(m.id) INTO current_usage
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.sender_type = 'user'  -- Only count user messages, NOT AI responses
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Backend only enforces actual subscription limits - frontend handles anonymous restrictions
  enforcement_limit := subscription_record.monthly_message_limit;
  
  -- Set display limits for marketing (show lower limits to encourage upgrades)
  IF is_anon THEN
    -- For anonymous users, show marketing limit but enforce subscription limit
    display_limit := 10;
  ELSE
    CASE subscription_record.subscription_tier
      WHEN 'Sample the Table' THEN
        display_limit := 10;  -- Show 10 instead of actual 40 for marketing
      WHEN 'Reserved Seat' THEN
        display_limit := subscription_record.monthly_message_limit;  -- Show actual limit for paid tiers
      WHEN 'VIP Table' THEN
        display_limit := subscription_record.monthly_message_limit;  -- Show actual limit for paid tiers
      ELSE
        display_limit := subscription_record.monthly_message_limit;  -- Default to actual limit
    END CASE;
  END IF;

  -- Check if within enforcement limits (actual subscription limits only)
  within_limits := current_usage < enforcement_limit;

  -- Prepare subscription info JSON with real-time usage count
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'messages_used', current_usage,  -- Use real-time count, not cached value
      'messages_limit', display_limit,  -- Show marketing limit to user
      'messages_limit_actual', enforcement_limit,  -- Include actual enforcement limit for internal use
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end,
      'is_anonymous', is_anon
    );

    -- Log for debugging
    RAISE NOTICE 'can_user_perform_action: user_id=%, is_anonymous=%, usage=%/% (display: %/%), within_limits=%, payg_credits=%', 
      user_uuid, 
      is_anon,
      current_usage, 
      enforcement_limit,
      current_usage,
      display_limit, 
      within_limits, 
      subscription_record.payg_credits_remaining;

    -- If within enforcement limits (subscription limits), allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over enforcement limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over enforcement limits and no credits
    RETURN QUERY SELECT FALSE, 'Message limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission for updated function
GRANT EXECUTE ON FUNCTION can_user_perform_action(UUID, TEXT) TO authenticated;

-- Add comment explaining the updated logic
COMMENT ON FUNCTION can_user_perform_action(UUID, TEXT) IS 'Updated to only enforce actual subscription limits. Frontend handles anonymous user restrictions. Supports display limits for marketing purposes.'; 