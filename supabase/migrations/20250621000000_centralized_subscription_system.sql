-- Migration to create centralized subscription system
-- Drop existing tables if they exist (clean slate)
DROP TABLE IF EXISTS public.payg_usage_log;
DROP TABLE IF EXISTS public.payg_credits;
DROP TABLE IF EXISTS public.user_usage_stats;

-- Create centralized user_subscriptions table
CREATE TABLE public.user_subscriptions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL UNIQUE,
  
  -- Subscription Information
  subscription_tier TEXT NOT NULL DEFAULT 'Sample the Table' CHECK (subscription_tier IN ('Sample the Table', 'Reserved Seat', 'VIP Table')),
  subscription_status TEXT NOT NULL DEFAULT 'active' CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'trialing')),
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  subscription_start_date TIMESTAMP WITH TIME ZONE,
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  
  -- Usage Limits (based on tier)
  monthly_conversation_limit INTEGER NOT NULL DEFAULT 20,
  monthly_message_limit INTEGER NOT NULL DEFAULT 40,
  
  -- Current Period Usage (calculated from conversations and messages)
  current_period_start TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT date_trunc('month', NOW()),
  current_period_end TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (date_trunc('month', NOW()) + interval '1 month' - interval '1 second'),
  conversations_used_this_period INTEGER NOT NULL DEFAULT 0,
  messages_used_this_period INTEGER NOT NULL DEFAULT 0,
  
  -- Pay-as-you-go Credits
  payg_credits_purchased INTEGER NOT NULL DEFAULT 0,
  payg_credits_used INTEGER NOT NULL DEFAULT 0,
  payg_credits_remaining INTEGER NOT NULL DEFAULT 0,
  payg_total_spent_cents INTEGER NOT NULL DEFAULT 0,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create pay-as-you-go purchase history table (for tracking individual purchases)
CREATE TABLE public.payg_purchase_history (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  subscription_id UUID REFERENCES public.user_subscriptions(id) NOT NULL,
  
  credits_purchased INTEGER NOT NULL,
  price_paid_cents INTEGER NOT NULL,
  stripe_payment_intent_id TEXT,
  purchase_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payg_purchase_history ENABLE ROW LEVEL SECURITY;

-- Create policies for user_subscriptions
CREATE POLICY "Users can view their own subscription" 
  ON public.user_subscriptions 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own subscription" 
  ON public.user_subscriptions 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all subscriptions" 
  ON public.user_subscriptions 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Create policies for payg_purchase_history
CREATE POLICY "Users can view their own purchase history" 
  ON public.payg_purchase_history 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service role can manage all purchase history" 
  ON public.payg_purchase_history 
  FOR ALL 
  USING (auth.role() = 'service_role');

-- Function to initialize user subscription when they sign up
CREATE OR REPLACE FUNCTION initialize_user_subscription()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_subscriptions (
    user_id,
    subscription_tier,
    monthly_conversation_limit,
    monthly_message_limit
  ) VALUES (
    NEW.id,
    'Sample the Table',
    20,
    40
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create subscription when user signs up
CREATE TRIGGER on_auth_user_created_subscription
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION initialize_user_subscription();

-- Function to update subscription tier limits
CREATE OR REPLACE FUNCTION update_subscription_tier_limits(user_uuid UUID, new_tier TEXT)
RETURNS VOID AS $$
DECLARE
  new_conversation_limit INTEGER;
  new_message_limit INTEGER;
BEGIN
  -- Set limits based on tier
  CASE new_tier
    WHEN 'Sample the Table' THEN
      new_conversation_limit := 20;
      new_message_limit := 40;
    WHEN 'Reserved Seat' THEN
      new_conversation_limit := 100;
      new_message_limit := 300;
    WHEN 'VIP Table' THEN
      new_conversation_limit := 1000;
      new_message_limit := 3000;
    ELSE
      RAISE EXCEPTION 'Invalid subscription tier: %', new_tier;
  END CASE;

  UPDATE public.user_subscriptions 
  SET 
    subscription_tier = new_tier,
    monthly_conversation_limit = new_conversation_limit,
    monthly_message_limit = new_message_limit,
    updated_at = NOW()
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current usage by counting actual conversations and messages
CREATE OR REPLACE FUNCTION refresh_user_usage(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
  subscription_record RECORD;
  conversation_count INTEGER;
  message_count INTEGER;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', user_uuid;
  END IF;

  -- Count conversations in current period
  SELECT COUNT(DISTINCT c.id) INTO conversation_count
  FROM public.conversations c
  WHERE c.user_id = user_uuid
    AND c.created_at >= subscription_record.current_period_start
    AND c.created_at <= subscription_record.current_period_end;

  -- Count messages in current period
  SELECT COUNT(m.id) INTO message_count
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Update subscription with current usage
  UPDATE public.user_subscriptions 
  SET 
    conversations_used_this_period = conversation_count,
    messages_used_this_period = message_count,
    updated_at = NOW()
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can perform action (conversation or message)
CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- 'conversation' or 'message'
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  limit_value INTEGER;
  within_limits BOOLEAN;
BEGIN
  -- Refresh usage first
  PERFORM refresh_user_usage(user_uuid);
  
  -- Get updated subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Check current usage against limits
  IF action_type = 'conversation' THEN
    current_usage := subscription_record.conversations_used_this_period;
    limit_value := subscription_record.monthly_conversation_limit;
  ELSIF action_type = 'message' THEN
    current_usage := subscription_record.messages_used_this_period;
    limit_value := subscription_record.monthly_message_limit;
  ELSE
    RETURN QUERY SELECT FALSE, 'Invalid action type'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  within_limits := current_usage < limit_value;

  -- Prepare subscription info JSON
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'conversations_used', subscription_record.conversations_used_this_period,
      'conversations_limit', subscription_record.monthly_conversation_limit,
      'messages_used', subscription_record.messages_used_this_period,
      'messages_limit', subscription_record.monthly_message_limit,
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end
    );

    -- If within limits, allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over limits and no credits
    RETURN QUERY SELECT FALSE, 'Subscription limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to consume pay-as-you-go credit
CREATE OR REPLACE FUNCTION consume_payg_credit(
  user_uuid UUID,
  action_type TEXT -- 'conversation' or 'message'
)
RETURNS TABLE(
  success BOOLEAN,
  credits_remaining INTEGER,
  error_message TEXT
) AS $$
DECLARE
  subscription_record RECORD;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 0, 'User subscription not found'::TEXT;
    RETURN;
  END IF;

  -- Check if user has credits
  IF subscription_record.payg_credits_remaining <= 0 THEN
    RETURN QUERY SELECT FALSE, 0, 'No pay-as-you-go credits remaining'::TEXT;
    RETURN;
  END IF;

  -- Consume one credit
  UPDATE public.user_subscriptions 
  SET 
    payg_credits_used = payg_credits_used + 1,
    payg_credits_remaining = payg_credits_remaining - 1,
    updated_at = NOW()
  WHERE user_id = user_uuid;

  -- Return success with remaining credits
  RETURN QUERY SELECT TRUE, (subscription_record.payg_credits_remaining - 1), NULL::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to add pay-as-you-go credits (for successful payments)
CREATE OR REPLACE FUNCTION add_payg_credits(
  user_uuid UUID,
  credits_amount INTEGER,
  price_paid_cents INTEGER,
  stripe_payment_intent_id TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  subscription_record RECORD;
  purchase_id UUID;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', user_uuid;
  END IF;

  -- Add credits to subscription
  UPDATE public.user_subscriptions 
  SET 
    payg_credits_purchased = payg_credits_purchased + credits_amount,
    payg_credits_remaining = payg_credits_remaining + credits_amount,
    payg_total_spent_cents = payg_total_spent_cents + price_paid_cents,
    updated_at = NOW()
  WHERE user_id = user_uuid;

  -- Record purchase history
  INSERT INTO public.payg_purchase_history (
    user_id,
    subscription_id,
    credits_purchased,
    price_paid_cents,
    stripe_payment_intent_id
  ) VALUES (
    user_uuid,
    subscription_record.id,
    credits_amount,
    price_paid_cents,
    stripe_payment_intent_id
  ) RETURNING id INTO purchase_id;

  RETURN purchase_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset monthly usage (called by cron job)
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS VOID AS $$
BEGIN
  UPDATE public.user_subscriptions 
  SET 
    current_period_start = date_trunc('month', NOW()),
    current_period_end = (date_trunc('month', NOW()) + interval '1 month' - interval '1 second'),
    conversations_used_this_period = 0,
    messages_used_this_period = 0,
    updated_at = NOW()
  WHERE current_period_end < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_period ON public.user_subscriptions(current_period_start, current_period_end);
CREATE INDEX IF NOT EXISTS idx_payg_purchase_history_user_id ON public.payg_purchase_history(user_id);
CREATE INDEX IF NOT EXISTS idx_payg_purchase_history_subscription_id ON public.payg_purchase_history(subscription_id);

-- Create a view for easy subscription overview
CREATE OR REPLACE VIEW user_subscription_overview AS
SELECT 
  us.user_id,
  p.email,
  p.full_name,
  us.subscription_tier,
  us.subscription_status,
  us.conversations_used_this_period,
  us.monthly_conversation_limit,
  us.messages_used_this_period,
  us.monthly_message_limit,
  us.payg_credits_remaining,
  us.payg_total_spent_cents,
  us.current_period_start,
  us.current_period_end,
  ROUND((us.conversations_used_this_period::DECIMAL / us.monthly_conversation_limit) * 100, 1) as conversation_usage_percent,
  ROUND((us.messages_used_this_period::DECIMAL / us.monthly_message_limit) * 100, 1) as message_usage_percent,
  (us.conversations_used_this_period >= us.monthly_conversation_limit) as conversation_limit_exceeded,
  (us.messages_used_this_period >= us.monthly_message_limit) as message_limit_exceeded
FROM public.user_subscriptions us
LEFT JOIN public.profiles p ON us.user_id = p.id; 