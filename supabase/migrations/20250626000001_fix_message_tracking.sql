-- Migration to fix message tracking inconsistency
-- Ensure refresh_user_usage function correctly counts only user messages

-- Drop and recreate the refresh_user_usage function to ensure consistency
DROP FUNCTION IF EXISTS refresh_user_usage(UUID);

-- Recreate the function with correct user message counting
CREATE OR R<PERSON>LACE FUNCTION refresh_user_usage(user_uuid UUID)
R<PERSON>URNS VOID AS $$
DECLARE
  subscription_record RECORD;
  user_message_count INTEGER;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', user_uuid;
  END IF;

  -- Count ONLY user messages in current period (exclude AI responses)
  -- This is critical: only count messages where sender_type = 'user'
  SELECT COUNT(m.id) INTO user_message_count
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.sender_type = 'user'  -- Only count user messages, NOT AI responses
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Update subscription with current usage (user messages only)
  UPDATE public.user_subscriptions 
  SET 
    messages_used_this_period = user_message_count,
    updated_at = NOW()
  WHERE user_id = user_uuid;

  -- Log for debugging (this will appear in database logs)
  RAISE NOTICE 'refresh_user_usage: user_id=%, user_messages=%, period=%-%', 
    user_uuid, 
    user_message_count, 
    subscription_record.current_period_start, 
    subscription_record.current_period_end;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the can_user_perform_action function to ensure it uses the correct refresh function
DROP FUNCTION IF EXISTS can_user_perform_action(UUID, TEXT);

CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- only 'message' is supported now
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  limit_value INTEGER;
  within_limits BOOLEAN;
BEGIN
  -- Only support 'message' action type now
  IF action_type != 'message' THEN
    RETURN QUERY SELECT FALSE, 'Only message action type is supported'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Refresh usage first to get accurate counts
  PERFORM refresh_user_usage(user_uuid);
  
  -- Get updated subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Check current usage against limits
  current_usage := subscription_record.messages_used_this_period;
  limit_value := subscription_record.monthly_message_limit;

  within_limits := current_usage < limit_value;

  -- Prepare subscription info JSON
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'messages_used', subscription_record.messages_used_this_period,
      'messages_limit', subscription_record.monthly_message_limit,
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end
    );

    -- Log for debugging
    RAISE NOTICE 'can_user_perform_action: user_id=%, usage=%/%, within_limits=%, payg_credits=%', 
      user_uuid, 
      current_usage, 
      limit_value, 
      within_limits, 
      subscription_record.payg_credits_remaining;

    -- If within limits, allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over limits and no credits
    RETURN QUERY SELECT FALSE, 'Message limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a debugging function to help troubleshoot message counting
CREATE OR REPLACE FUNCTION debug_user_message_count(user_uuid UUID)
RETURNS TABLE(
  total_messages INTEGER,
  user_messages INTEGER,
  ai_messages INTEGER,
  current_period_user_messages INTEGER,
  subscription_messages_used INTEGER,
  period_start TIMESTAMP WITH TIME ZONE,
  period_end TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
  subscription_record RECORD;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', user_uuid;
  END IF;

  RETURN QUERY 
  SELECT 
    -- Total messages (all types)
    (SELECT COUNT(*)::INTEGER 
     FROM public.messages m 
     JOIN public.conversations c ON m.conversation_id = c.id 
     WHERE c.user_id = user_uuid) as total_messages,
    
    -- User messages (all time)
    (SELECT COUNT(*)::INTEGER 
     FROM public.messages m 
     JOIN public.conversations c ON m.conversation_id = c.id 
     WHERE c.user_id = user_uuid AND m.sender_type = 'user') as user_messages,
    
    -- AI messages (all time)
    (SELECT COUNT(*)::INTEGER 
     FROM public.messages m 
     JOIN public.conversations c ON m.conversation_id = c.id 
     WHERE c.user_id = user_uuid AND m.sender_type = 'character') as ai_messages,
    
    -- User messages in current period
    (SELECT COUNT(*)::INTEGER 
     FROM public.messages m 
     JOIN public.conversations c ON m.conversation_id = c.id 
     WHERE c.user_id = user_uuid 
       AND m.sender_type = 'user'
       AND m.created_at >= subscription_record.current_period_start
       AND m.created_at <= subscription_record.current_period_end) as current_period_user_messages,
    
    -- What's stored in subscription table
    subscription_record.messages_used_this_period as subscription_messages_used,
    
    -- Period bounds
    subscription_record.current_period_start as period_start,
    subscription_record.current_period_end as period_end;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION refresh_user_usage(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION can_user_perform_action(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION debug_user_message_count(UUID) TO authenticated; 