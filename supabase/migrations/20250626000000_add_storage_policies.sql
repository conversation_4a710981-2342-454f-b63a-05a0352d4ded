-- Add storage RLS policies for hollywood-table bucket
-- These policies are required for file uploads to work in Supabase Storage

-- Policy to allow authenticated users to upload files to hollywood-table bucket
CREATE POLICY "Allow authenticated users to upload to hollywood-table" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (bucket_id = 'hollywood-table');

-- Policy to allow authenticated users to view their own uploaded files
CREATE POLICY "Allow authenticated users to view files in hollywood-table" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (bucket_id = 'hollywood-table');

-- Policy to allow authenticated users to update their own files
CREATE POLICY "Allow authenticated users to update files in hollywood-table" 
ON storage.objects 
FOR UPDATE 
TO authenticated 
USING (bucket_id = 'hollywood-table' AND owner_id::uuid = auth.uid());

-- Policy to allow authenticated users to delete their own files
CREATE POLICY "Allow authenticated users to delete their own files in hollywood-table" 
ON storage.objects 
FOR DELETE 
TO authenticated 
USING (bucket_id = 'hollywood-table' AND owner_id::uuid = auth.uid());

-- Optional: Policy for more restrictive avatar uploads (only to avatars folder and user's own folder)
CREATE POLICY "Allow users to upload avatars to their own folder" 
ON storage.objects 
FOR INSERT 
TO authenticated 
WITH CHECK (
  bucket_id = 'hollywood-table' 
  AND (storage.foldername(name))[1] = 'avatars'
  AND (storage.foldername(name))[2] = auth.uid()::text
);

-- Optional: Policy for users to view their own avatars
CREATE POLICY "Allow users to view their own avatars" 
ON storage.objects 
FOR SELECT 
TO authenticated 
USING (
  bucket_id = 'hollywood-table' 
  AND (storage.foldername(name))[1] = 'avatars'
  AND (storage.foldername(name))[2] = auth.uid()::text
); 