-- Migration to remove conversation count tracking
-- Only track messages for usage limits

-- Drop existing functions that track conversations
DROP FUNCTION IF EXISTS can_user_perform_action(UUID, TEXT);
DROP FUNCTION IF EXISTS refresh_user_usage(UUID);
DROP FUNCTION IF EXISTS update_subscription_tier_limits(UUID, TEXT);

-- Remove conversation-related columns from user_subscriptions
ALTER TABLE public.user_subscriptions 
DROP COLUMN IF EXISTS monthly_conversation_limit,
DROP COLUMN IF EXISTS conversations_used_this_period;

-- Update subscription tier limits function (messages only)
CREATE OR REPLACE FUNCTION update_subscription_tier_limits(user_uuid UUID, new_tier TEXT)
RETURNS VOID AS $$
DECLARE
  new_message_limit INTEGER;
BEGIN
  -- Set limits based on tier (messages only)
  CASE new_tier
    WHEN 'Sample the Table' THEN
      new_message_limit := 40;
    WHEN 'Reserved Seat' THEN
      new_message_limit := 300;
    WHEN 'VIP Table' THEN
      new_message_limit := 3000;
    ELSE
      RAISE EXCEPTION 'Invalid subscription tier: %', new_tier;
  END CASE;

  UPDATE public.user_subscriptions 
  SET 
    subscription_tier = new_tier,
    monthly_message_limit = new_message_limit,
    updated_at = NOW()
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update usage refresh function (messages only)
CREATE OR REPLACE FUNCTION refresh_user_usage(user_uuid UUID)
RETURNS VOID AS $$
DECLARE
  subscription_record RECORD;
  message_count INTEGER;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RAISE EXCEPTION 'User subscription not found for user: %', user_uuid;
  END IF;

  -- Count only user messages in current period (not AI responses)
  SELECT COUNT(m.id) INTO message_count
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.sender_type = 'user'
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- Update subscription with current usage (messages only)
  UPDATE public.user_subscriptions 
  SET 
    messages_used_this_period = message_count,
    updated_at = NOW()
  WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update action check function (messages only)
CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- only 'message' is supported now
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  limit_value INTEGER;
  within_limits BOOLEAN;
BEGIN
  -- Only support 'message' action type now
  IF action_type != 'message' THEN
    RETURN QUERY SELECT FALSE, 'Only message action type is supported'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Refresh usage first
  PERFORM refresh_user_usage(user_uuid);
  
  -- Get updated subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Check current usage against limits
  current_usage := subscription_record.messages_used_this_period;
  limit_value := subscription_record.monthly_message_limit;

  within_limits := current_usage < limit_value;

  -- Prepare subscription info JSON
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'messages_used', subscription_record.messages_used_this_period,
      'messages_limit', subscription_record.monthly_message_limit,
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end
    );

    -- If within limits, allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over limits and no credits
    RETURN QUERY SELECT FALSE, 'Message limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the user subscription initialization function
CREATE OR REPLACE FUNCTION initialize_user_subscription()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_subscriptions (
    user_id,
    subscription_tier,
    monthly_message_limit
  ) VALUES (
    NEW.id,
    'Sample the Table',
    40
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update monthly usage reset function (messages only)
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS VOID AS $$
BEGIN
  UPDATE public.user_subscriptions 
  SET 
    current_period_start = date_trunc('month', NOW()),
    current_period_end = (date_trunc('month', NOW()) + interval '1 month' - interval '1 second'),
    messages_used_this_period = 0,
    updated_at = NOW()
  WHERE current_period_end < NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the subscription overview view
CREATE OR REPLACE VIEW user_subscription_overview AS
SELECT 
  us.user_id,
  p.email,
  p.full_name,
  us.subscription_tier,
  us.subscription_status,
  us.messages_used_this_period,
  us.monthly_message_limit,
  us.payg_credits_remaining,
  us.payg_total_spent_cents,
  us.current_period_start,
  us.current_period_end,
  ROUND((us.messages_used_this_period::DECIMAL / us.monthly_message_limit) * 100, 1) as message_usage_percent,
  (us.messages_used_this_period >= us.monthly_message_limit) as message_limit_exceeded
FROM public.user_subscriptions us
LEFT JOIN public.profiles p ON us.user_id = p.id;
