
-- Create waitlist table to store user signups
CREATE TABLE public.waitlist (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  notified BOOLEAN DEFAULT FALSE
);

-- Create invite codes table
CREATE TABLE public.invite_codes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  code TEXT NOT NULL UNIQUE,
  used BOOLEAN DEFAULT FALSE,
  used_by UUID REFERENCES auth.users,
  used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE
);

-- Enable RLS on both tables
ALTER TABLE public.waitlist ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invite_codes ENABLE ROW LEVEL SECURITY;

-- Policies for waitlist (public can insert, admin can view all)
CREATE POLICY "Anyone can join waitlist" 
  ON public.waitlist 
  FOR INSERT 
  WITH CHECK (true);

-- Policies for invite codes (public can check validity, authenticated users can use)
CREATE POLICY "Anyone can check invite codes" 
  ON public.invite_codes 
  FOR SELECT 
  USING (true);

CREATE POLICY "Authenticated users can update invite codes" 
  ON public.invite_codes 
  FOR UPDATE 
  USING (auth.uid() IS NOT NULL);

-- Function to validate and use invite code
CREATE OR REPLACE FUNCTION public.use_invite_code(invite_code TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
DECLARE
  code_exists BOOLEAN := FALSE;
  code_used BOOLEAN := FALSE;
  code_expired BOOLEAN := FALSE;
BEGIN
  -- Check if code exists and get its status
  SELECT 
    TRUE,
    used,
    (expires_at IS NOT NULL AND expires_at < NOW())
  INTO code_exists, code_used, code_expired
  FROM public.invite_codes 
  WHERE code = invite_code;
  
  -- Return false if code doesn't exist, is used, or expired
  IF NOT code_exists OR code_used OR code_expired THEN
    RETURN FALSE;
  END IF;
  
  -- Mark code as used
  UPDATE public.invite_codes 
  SET 
    used = TRUE,
    used_by = auth.uid(),
    used_at = NOW()
  WHERE code = invite_code;
  
  RETURN TRUE;
END;
$$;

-- Insert some sample invite codes for testing
INSERT INTO public.invite_codes (code, expires_at) VALUES 
  ('HOLLYWOOD2024', NOW() + INTERVAL '30 days'),
  ('SCREENWRITER', NOW() + INTERVAL '30 days'),
  ('BETA123', NOW() + INTERVAL '30 days');
