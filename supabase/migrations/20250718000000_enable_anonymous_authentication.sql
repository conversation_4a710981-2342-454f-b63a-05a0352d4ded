-- Migration to enable anonymous authentication and update RLS policies
-- This migration enables Supabase anonymous authentication and ensures all existing
-- RLS policies work correctly with anonymous users

-- Enable anonymous authentication (this is typically done in Supabase dashboard)
-- But we can document the required settings here for reference:
-- 
-- In Supabase Dashboard > Authentication > Settings:
-- - Enable "Allow anonymous sign-ins" 
-- - Set "Anonymous sign-in" to enabled
--
-- This migration focuses on database-level changes to support anonymous users

-- Update the initialize_user_subscription function to handle anonymous users
CREATE OR REPLACE FUNCTION initialize_user_subscription()
RETURNS TRIGGER AS $$
BEGIN
  -- Create subscription for all users (including anonymous)
  INSERT INTO public.user_subscriptions (
    user_id,
    subscription_tier,
    monthly_conversation_limit,
    monthly_message_limit
  ) VALUES (
    NEW.id,
    'Sample the Table',
    20,
    40
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure the trigger works for anonymous users too
-- (The existing trigger should already work, but let's make sure)
DROP TRIGGER IF EXISTS on_auth_user_created_subscription ON auth.users;
CREATE TRIGGER on_auth_user_created_subscription
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION initialize_user_subscription();

-- Update RLS policies to explicitly support anonymous users
-- Most existing policies use auth.uid() which works for anonymous users,
-- but let's add some explicit policies for clarity

-- Conversations table - ensure anonymous users can access their conversations
DROP POLICY IF EXISTS "Users can view their own conversations" ON public.conversations;
CREATE POLICY "Users can view their own conversations" 
  ON public.conversations 
  FOR SELECT 
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create their own conversations" ON public.conversations;
CREATE POLICY "Users can create their own conversations" 
  ON public.conversations 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own conversations" ON public.conversations;
CREATE POLICY "Users can update their own conversations" 
  ON public.conversations 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Messages table - ensure anonymous users can access their messages
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON public.messages;
CREATE POLICY "Users can view messages in their conversations" 
  ON public.messages 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

DROP POLICY IF EXISTS "Users can create messages in their conversations" ON public.messages;
CREATE POLICY "Users can create messages in their conversations" 
  ON public.messages 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.conversations 
      WHERE id = conversation_id AND user_id = auth.uid()
    )
  );

-- Profiles table - ensure anonymous users can manage their profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
CREATE POLICY "Users can view their own profile" 
  ON public.profiles 
  FOR SELECT 
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
CREATE POLICY "Users can update their own profile" 
  ON public.profiles 
  FOR UPDATE 
  USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert their own profile" ON public.profiles;
CREATE POLICY "Users can insert their own profile" 
  ON public.profiles 
  FOR INSERT 
  WITH CHECK (auth.uid() = id);

-- User subscriptions - policies already exist and should work with anonymous users
-- But let's ensure they're explicit about supporting all authenticated users (including anonymous)
DROP POLICY IF EXISTS "Users can view their own subscription" ON public.user_subscriptions;
CREATE POLICY "Users can view their own subscription" 
  ON public.user_subscriptions 
  FOR SELECT 
  USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own subscription" ON public.user_subscriptions;
CREATE POLICY "Users can update their own subscription" 
  ON public.user_subscriptions 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- PAYG messages table - ensure anonymous users can view their PAYG usage
DROP POLICY IF EXISTS "Users can view their own payg messages" ON public.payg_messages;
CREATE POLICY "Users can view their own payg messages" 
  ON public.payg_messages 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- PAYG purchase history - ensure anonymous users can view their purchase history
DROP POLICY IF EXISTS "Users can view their own purchase history" ON public.payg_purchase_history;
CREATE POLICY "Users can view their own purchase history" 
  ON public.payg_purchase_history 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Create a function to check if a user is anonymous
CREATE OR REPLACE FUNCTION is_anonymous_user(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- Get user information from auth.users
  SELECT * INTO user_record 
  FROM auth.users 
  WHERE id = user_uuid;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Anonymous users have no email and are_anonymous is true in raw_user_meta_data
  RETURN (
    user_record.email IS NULL AND 
    (user_record.raw_user_meta_data->>'is_anonymous')::BOOLEAN = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to upgrade anonymous user to email-based account
CREATE OR REPLACE FUNCTION upgrade_anonymous_to_email(
  user_uuid UUID,
  new_email TEXT
)
RETURNS TABLE(
  success BOOLEAN,
  error_message TEXT
) AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- Verify user exists and is anonymous
  SELECT * INTO user_record 
  FROM auth.users 
  WHERE id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User not found'::TEXT;
    RETURN;
  END IF;

  -- Check if user is actually anonymous
  IF NOT is_anonymous_user(user_uuid) THEN
    RETURN QUERY SELECT FALSE, 'User is not anonymous'::TEXT;
    RETURN;
  END IF;

  -- Check if email is already in use
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = new_email AND id != user_uuid) THEN
    RETURN QUERY SELECT FALSE, 'Email already in use'::TEXT;
    RETURN;
  END IF;

  -- This function documents the upgrade process, but the actual email update
  -- must be done via Supabase Admin API in the application code
  -- We cannot directly update auth.users from SQL functions for security reasons
  
  RETURN QUERY SELECT TRUE, 'Ready for email upgrade via Admin API'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions for new functions
GRANT EXECUTE ON FUNCTION is_anonymous_user(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION upgrade_anonymous_to_email(UUID, TEXT) TO authenticated;

-- Update the can_user_perform_action function to work with anonymous users
-- (The existing function should already work, but let's ensure it handles anonymous users properly)
CREATE OR REPLACE FUNCTION can_user_perform_action(
  user_uuid UUID,
  action_type TEXT -- only 'message' is supported now
)
RETURNS TABLE(
  can_perform BOOLEAN,
  reason TEXT,
  using_payg BOOLEAN,
  subscription_info JSON
) AS $$
DECLARE
  subscription_record RECORD;
  current_usage INTEGER;
  limit_value INTEGER;
  within_limits BOOLEAN;
  is_anon BOOLEAN;
BEGIN
  -- Only support 'message' action type now
  IF action_type != 'message' THEN
    RETURN QUERY SELECT FALSE, 'Only message action type is supported'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Check if user is anonymous
  is_anon := is_anonymous_user(user_uuid);

  -- Get subscription info
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, FALSE, '{}'::JSON;
    RETURN;
  END IF;

  -- Count current user messages directly from messages table (real-time count)
  SELECT COUNT(m.id) INTO current_usage
  FROM public.messages m
  JOIN public.conversations c ON m.conversation_id = c.id
  WHERE c.user_id = user_uuid
    AND m.sender_type = 'user'  -- Only count user messages, NOT AI responses
    AND m.created_at >= subscription_record.current_period_start
    AND m.created_at <= subscription_record.current_period_end;

  -- For anonymous users, enforce a 3-message limit instead of the subscription limit
  IF is_anon THEN
    limit_value := 3;
  ELSE
    limit_value := subscription_record.monthly_message_limit;
  END IF;

  within_limits := current_usage < limit_value;

  -- Prepare subscription info JSON with real-time usage count
  DECLARE
    sub_info JSON;
  BEGIN
    sub_info := JSON_BUILD_OBJECT(
      'tier', subscription_record.subscription_tier,
      'status', subscription_record.subscription_status,
      'messages_used', current_usage,  -- Use real-time count, not cached value
      'messages_limit', CASE WHEN is_anon THEN 3 ELSE subscription_record.monthly_message_limit END,
      'payg_credits_remaining', subscription_record.payg_credits_remaining,
      'period_start', subscription_record.current_period_start,
      'period_end', subscription_record.current_period_end,
      'is_anonymous', is_anon
    );

    -- Log for debugging
    RAISE NOTICE 'can_user_perform_action: user_id=%, is_anonymous=%, real_time_usage=%/%, within_limits=%, payg_credits=%', 
      user_uuid, 
      is_anon,
      current_usage, 
      limit_value, 
      within_limits, 
      subscription_record.payg_credits_remaining;

    -- If within limits, allow action
    IF within_limits THEN
      RETURN QUERY SELECT TRUE, 'Within subscription limits'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- For anonymous users who hit the 3-message limit, require email upgrade
    IF is_anon THEN
      RETURN QUERY SELECT FALSE, 'Anonymous user message limit reached - email required'::TEXT, FALSE, sub_info;
      RETURN;
    END IF;

    -- If over limits but has pay-as-you-go credits
    IF subscription_record.payg_credits_remaining > 0 THEN
      RETURN QUERY SELECT TRUE, 'Using pay-as-you-go credits'::TEXT, TRUE, sub_info;
      RETURN;
    END IF;

    -- Over limits and no credits
    RETURN QUERY SELECT FALSE, 'Message limit exceeded and no pay-as-you-go credits available'::TEXT, FALSE, sub_info;
  END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission for updated function
GRANT EXECUTE ON FUNCTION can_user_perform_action(UUID, TEXT) TO authenticated;

-- Add comment explaining the migration
COMMENT ON FUNCTION is_anonymous_user(UUID) IS 'Checks if a user is an anonymous user based on their auth metadata';
COMMENT ON FUNCTION upgrade_anonymous_to_email(UUID, TEXT) IS 'Validates anonymous user email upgrade requirements. Actual upgrade must be done via Supabase Admin API.';
COMMENT ON FUNCTION can_user_perform_action(UUID, TEXT) IS 'Updated to enforce 3-message limit for anonymous users and require email upgrade instead of PAYG credits.';

-- Create a view to easily identify anonymous users
CREATE OR REPLACE VIEW anonymous_users AS
SELECT 
  u.id,
  u.created_at,
  u.last_sign_in_at,
  us.messages_used_this_period,
  us.payg_credits_remaining
FROM auth.users u
JOIN public.user_subscriptions us ON u.id = us.user_id
WHERE u.email IS NULL 
  AND (u.raw_user_meta_data->>'is_anonymous')::BOOLEAN = TRUE;

COMMENT ON VIEW anonymous_users IS 'View showing all anonymous users and their usage statistics';