-- Add table to track processed webhook events for deduplication
CREATE TABLE IF NOT EXISTS processed_webhook_events (
  id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
  stripe_event_id text NOT NULL UNIQUE,
  event_type text NOT NULL,
  processed_at timestamp with time zone NOT NULL DEFAULT now(),
  created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Add index for faster lookups
CREATE INDEX IF NOT EXISTS idx_processed_webhook_events_stripe_event_id 
ON processed_webhook_events(stripe_event_id);

-- Add index for cleanup queries (optional - for removing old processed events)
CREATE INDEX IF NOT EXISTS idx_processed_webhook_events_processed_at 
ON processed_webhook_events(processed_at);

-- Add RLS policies if needed (assuming this is an internal table)
ALTER TABLE processed_webhook_events ENABLE ROW LEVEL SECURITY;

-- Allow service role to manage processed events
CREATE POLICY "Service role can manage processed webhook events" 
ON processed_webhook_events 
FOR ALL 
TO service_role 
USING (true) 
WITH CHECK (true);

-- Optional: Add a function to clean up old processed events (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_processed_webhook_events()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM processed_webhook_events 
  WHERE processed_at < (now() - interval '30 days');
END;
$$;

-- Comment on table and important columns
COMMENT ON TABLE processed_webhook_events IS 'Tracks processed Stripe webhook events to prevent duplicate processing';
COMMENT ON COLUMN processed_webhook_events.stripe_event_id IS 'The unique event ID from Stripe';
COMMENT ON COLUMN processed_webhook_events.event_type IS 'The type of Stripe event (e.g., customer.subscription.created)';
COMMENT ON COLUMN processed_webhook_events.processed_at IS 'When the event was successfully processed';