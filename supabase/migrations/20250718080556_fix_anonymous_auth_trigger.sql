-- Fix anonymous authentication trigger
-- The issue is that our initialize_user_subscription function needs to handle anonymous users properly

-- Update the initialize_user_subscription function to handle anonymous users
CREATE OR REPLACE FUNCTION initialize_user_subscription()
RETURNS TRIGGER AS $$
BEGIN
  -- Create subscription for all users (including anonymous)
  -- Use INSERT with ON CONFLICT to avoid duplicate key errors
  INSERT INTO public.user_subscriptions (
    user_id,
    subscription_tier,
    monthly_message_limit,
    current_period_start,
    current_period_end
  ) VALUES (
    NEW.id,
    'Sample the Table',
    40,
    date_trunc('month', NOW()),
    (date_trunc('month', NOW()) + interval '1 month' - interval '1 second')
  )
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log the error but don't fail the user creation
    RAISE NOTICE 'Error in initialize_user_subscription for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user is anonymous
CREATE OR REPLACE FUNCTION is_anonymous_user(user_uuid UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
DECLARE
  user_record RECORD;
BEGIN
  -- Get user information from auth.users
  SELECT * INTO user_record 
  FROM auth.users 
  WHERE id = user_uuid;

  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Anonymous users have no email and is_anonymous is true
  RETURN (
    user_record.email IS NULL AND 
    user_record.is_anonymous = TRUE
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION is_anonymous_user(UUID) TO authenticated;
