-- Migration to improve subscription cancellation workflow
-- Allow cancel_at_period_end status and add yearly billing cycle support

-- Add billing_cycle column to support both monthly and yearly subscriptions
ALTER TABLE public.user_subscriptions 
ADD COLUMN IF NOT EXISTS billing_cycle TEXT DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'annual'));

-- Add 'cancel_at_period_end' to allowed subscription status values
ALTER TABLE public.user_subscriptions 
DROP CONSTRAINT IF EXISTS user_subscriptions_subscription_status_check;

ALTER TABLE public.user_subscriptions 
ADD CONSTRAINT user_subscriptions_subscription_status_check 
CHECK (subscription_status IN ('active', 'canceled', 'past_due', 'trialing', 'cancel_at_period_end'));

-- Function to cancel subscription and move to free tier while keeping usage
CREATE OR REPLACE FUNCTION cancel_subscription_to_free_tier(user_uuid UUID)
RETURNS TABLE(
  success BOOLEAN,
  message TEXT,
  previous_tier TEXT,
  new_tier TEXT,
  usage_kept BOOLEAN
) AS $$
DECLARE
  subscription_record RECORD;
  old_tier TEXT;
BEGIN
  -- Get user subscription
  SELECT * INTO subscription_record 
  FROM public.user_subscriptions 
  WHERE user_id = user_uuid;

  IF NOT FOUND THEN
    RETURN QUERY SELECT FALSE, 'User subscription not found'::TEXT, NULL::TEXT, NULL::TEXT, FALSE;
    RETURN;
  END IF;

  -- Check if already on free tier
  IF subscription_record.subscription_tier = 'Sample the Table' THEN
    RETURN QUERY SELECT FALSE, 'Already on free tier'::TEXT, subscription_record.subscription_tier, subscription_record.subscription_tier, FALSE;
    RETURN;
  END IF;

  old_tier := subscription_record.subscription_tier;

  -- Move to free tier immediately but keep current usage
  UPDATE public.user_subscriptions 
  SET 
    subscription_tier = 'Sample the Table',
    subscription_status = 'cancel_at_period_end',
    monthly_conversation_limit = 20,
    monthly_message_limit = 40,
    billing_cycle = 'monthly', -- Reset to monthly for free tier
    -- Keep current usage intact - do NOT reset it
    updated_at = NOW()
  WHERE user_id = user_uuid;

  RETURN QUERY SELECT 
    TRUE, 
    'Subscription cancelled. Moved to free tier but keeping current usage until next billing cycle.'::TEXT,
    old_tier,
    'Sample the Table'::TEXT,
    TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the reset_monthly_usage function to handle both monthly and yearly billing cycles
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS VOID AS $$
BEGIN
  -- Handle users with monthly billing cycles
  UPDATE public.user_subscriptions 
  SET 
    current_period_start = date_trunc('month', NOW()),
    current_period_end = (date_trunc('month', NOW()) + interval '1 month' - interval '1 second'),
    conversations_used_this_period = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 0
      ELSE 0 
    END,
    messages_used_this_period = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 0
      ELSE 0 
    END,
    subscription_status = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 'active'
      ELSE subscription_status 
    END,
    updated_at = NOW()
  WHERE current_period_end < NOW()
    AND (billing_cycle = 'monthly' OR billing_cycle IS NULL); -- Handle legacy records without billing_cycle

  -- Handle users with yearly/annual billing cycles
  UPDATE public.user_subscriptions 
  SET 
    current_period_start = date_trunc('year', NOW()),
    current_period_end = (date_trunc('year', NOW()) + interval '1 year' - interval '1 second'),
    conversations_used_this_period = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 0
      ELSE 0 
    END,
    messages_used_this_period = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 0
      ELSE 0 
    END,
    subscription_status = CASE 
      WHEN subscription_status = 'cancel_at_period_end' THEN 'active'
      ELSE subscription_status 
    END,
    updated_at = NOW()
  WHERE current_period_end < NOW()
    AND billing_cycle = 'annual';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to set billing cycle for a user subscription
CREATE OR REPLACE FUNCTION set_billing_cycle(user_uuid UUID, cycle TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  new_period_start TIMESTAMP WITH TIME ZONE;
  new_period_end TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Validate billing cycle
  IF cycle NOT IN ('monthly', 'annual') THEN
    RAISE EXCEPTION 'Invalid billing cycle. Must be monthly or annual.';
  END IF;

  -- Calculate new period based on billing cycle
  IF cycle = 'monthly' THEN
    new_period_start := date_trunc('month', NOW());
    new_period_end := (date_trunc('month', NOW()) + interval '1 month' - interval '1 second');
  ELSE -- annual
    new_period_start := date_trunc('year', NOW());
    new_period_end := (date_trunc('year', NOW()) + interval '1 year' - interval '1 second');
  END IF;

  -- Update the subscription
  UPDATE public.user_subscriptions 
  SET 
    billing_cycle = cycle,
    current_period_start = new_period_start,
    current_period_end = new_period_end,
    updated_at = NOW()
  WHERE user_id = user_uuid;

  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create index on billing_cycle for performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_billing_cycle ON public.user_subscriptions(billing_cycle); 