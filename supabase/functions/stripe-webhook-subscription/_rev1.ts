import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";
import { Stripe } from "../_shared/stripe.ts";
import { createStripeClient } from "../_shared/stripe.ts";
import { getStripePriceId, getSubscriptionTierFromPriceId, isPaygPriceId } from "../_shared/pricing.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[STRIPE-WEBHOOK-SUBSCRIPTION] ${step}${detailsStr}`);
};

// Validate required environment variables at startup
const validateEnvironment = () => {
  const isProduction = Deno.env.get("NODE_ENV") === "production";
  
  const required = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  
  // Check for environment-specific Stripe key and webhook secret
  const stripeKey = isProduction ? 'STRIPE_LIVE_SECRET_KEY' : 'STRIPE_TEST_SECRET_KEY';
  const webhookSecret = isProduction ? 'STRIPE_LIVE_WEBHOOK_SECRET_SUBSCRIPTION' : 'STRIPE_TEST_WEBHOOK_SECRET_SUBSCRIPTION';
  
  required.push(stripeKey, webhookSecret);
  
  const missing = required.filter(key => !Deno.env.get(key));
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};

// Check if event has already been processed to prevent duplicates
const isEventProcessed = async (supabaseClient: any, eventId: string): Promise<boolean> => {
  try {
    const { data } = await supabaseClient
      .from('processed_webhook_events')
      .select('id')
      .eq('stripe_event_id', eventId)
      .single();
    
    return !!data;
  } catch (error) {
    // If table doesn't exist or other error, assume not processed
    return false;
  }
};

// Mark event as processed
const markEventAsProcessed = async (supabaseClient: any, eventId: string, eventType: string) => {
  try {
    await supabaseClient
      .from('processed_webhook_events')
      .insert({
        stripe_event_id: eventId,
        event_type: eventType,
        processed_at: new Date().toISOString()
      });
  } catch (error) {
    // Log error but don't fail the webhook - the processing was successful
    console.warn('Failed to mark event as processed:', error);
  }
};

// Handle PAYG (pay-as-you-go) credit purchases
const handlePaygPurchase = async (
  invoice: any, 
  stripe: any, 
  supabaseClient: any
): Promise<Response | null> => {
  logStep("Processing PAYG purchase", { invoiceId: invoice.id });

  // For PAYG purchases, get user ID from customer metadata or invoice metadata
  const customer = await stripe.customers.retrieve(invoice.customer as string);
  const userId = (customer as any).metadata?.supabase_user_id || invoice.metadata?.supabase_user_id;
  
  if (!userId) {
    logStep("No supabase_user_id found for PAYG purchase", { 
      customerId: invoice.customer,
      invoiceId: invoice.id 
    });
    return new Response("Missing user ID for PAYG purchase", { status: 400 });
  }

  // Check if user subscription record exists (free users might not have one)
  const { data: existingSubscription, error: fetchError } = await supabaseClient
    .from("user_subscriptions")
    .select("*")
    .eq("user_id", userId)
    .single();

  if (fetchError && fetchError.code !== 'PGRST116') {
    logStep("Error fetching user subscription for PAYG", { error: fetchError });
    return new Response("Database error", { status: 500 });
  }

  // If no subscription record exists, create one for the free user
  if (!existingSubscription) {
    const subscriptionData = {
      user_id: userId,
      stripe_customer_id: invoice.customer as string,
      subscription_tier: 'Sample the Table', // Default tier for free users
      subscription_status: 'active',
      subscription_start_date: new Date().toISOString(),
      subscription_end_date: null, // Free tier doesn't expire
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { error: createError } = await supabaseClient
      .from("user_subscriptions")
      .insert(subscriptionData);

    if (createError) {
      logStep("Error creating user subscription for free user", { error: createError });
      return new Response("Failed to create subscription record", { status: 500 });
    }

    logStep("Created subscription record for free user", { 
      userId, 
      tier: 'Sample the Table' 
    });

    // Update subscription tier limits for new free user
    await supabaseClient.rpc("update_subscription_tier_limits", {
      user_uuid: userId,
      new_tier: 'Sample the Table'
    });
  }

  // Add PAYG credits
  await supabaseClient.rpc("add_payg_credits", {
    user_uuid: userId,
    invoice_id: invoice.id
  });

  logStep("PAYG credits added successfully", { 
    userId, 
    invoiceId: invoice.id 
  });
  
  return null; // Success - no error response
};

// Handle subscription upgrades/renewals
const handleSubscriptionUpgrade = async (
  invoice: any, 
  stripe: any, 
  supabaseClient: any
): Promise<Response | null> => {
  logStep("Processing subscription upgrade/renewal", { invoiceId: invoice.id });

  if (!invoice.subscription) {
    logStep("Invoice not related to subscription, skipping", { invoiceId: invoice.id });
    return null; // Not an error, just skip
  }

  const subscriptionId = invoice.lines.data[0].parent.subscription_item_details.subscription;

  // Get the full subscription details from Stripe
  const subscription = await stripe.subscriptions.retrieve(subscriptionId as string);
  
  logStep("Retrieved subscription details", { 
    subscriptionId: subscription.id,
    status: subscription.status,
    metadata: subscription.metadata 
  });

  // Extract user ID from subscription metadata
  const userId = subscription.metadata?.supabase_user_id;
  if (!userId) {
    logStep("No supabase_user_id in subscription metadata", { 
      subscriptionId: subscription.id,
      metadata: subscription.metadata 
    });
    return new Response("Missing user ID in subscription metadata", { status: 400 });
  }

  // Determine subscription tier from the price ID in the invoice line items
  let subscriptionTier: string | null = null;
  
  for (const lineItem of invoice.lines.data) {
    if (lineItem.parent?.subscription_item_details?.subscription === subscription.id) {
      const priceId = lineItem.pricing?.price_details?.price;
      if (priceId) {
        subscriptionTier = getSubscriptionTierFromPriceId(priceId);
        if (subscriptionTier) {
          logStep("Identified subscription tier from price ID", { 
            priceId, 
            tier: subscriptionTier 
          });
          break;
        }
      }
    }
  }

  // Fallback: try to get tier from subscription metadata
  if (!subscriptionTier && subscription.metadata?.subscription_tier) {
    subscriptionTier = subscription.metadata.subscription_tier;
    logStep("Using subscription tier from metadata", { tier: subscriptionTier });
  }

  if (!subscriptionTier) {
    logStep("Could not determine subscription tier", { 
      subscriptionId: subscription.id,
      lineItems: invoice.lines.data 
    });
    return new Response("Could not determine subscription tier", { status: 400 });
  }

  // Check if user subscription record exists
  const { data: existingSubscription, error: fetchError } = await supabaseClient
    .from("user_subscriptions")
    .select("*")
    .eq("user_id", userId)
    .single();

  if (fetchError && fetchError.code !== 'PGRST116') {
    logStep("Error fetching user subscription", { error: fetchError });
    return new Response("Database error", { status: 500 });
  }

  // Update subscription tier limits
  await supabaseClient.rpc("update_subscription_tier_limits", {
    user_uuid: userId,
    new_tier: subscriptionTier
  });

  const subscriptionData = {
    stripe_subscription_id: subscription.id,
    stripe_customer_id: subscription.customer as string,
    subscription_tier: subscriptionTier,
    subscription_status: subscription.status,
    subscription_start_date: new Date(subscription.current_period_start * 1000).toISOString(),
    subscription_end_date: new Date(subscription.current_period_end * 1000).toISOString(),
    updated_at: new Date().toISOString()
  };

  if (existingSubscription) {
    // Update existing subscription
    const { error: updateError } = await supabaseClient
      .from("user_subscriptions")
      .update(subscriptionData)
      .eq("user_id", userId);

    if (updateError) {
      logStep("Error updating user subscription", { error: updateError });
      return new Response("Failed to update subscription", { status: 500 });
    }

    logStep("Updated existing subscription", { 
      userId, 
      tier: subscriptionTier, 
      subscriptionId: subscription.id 
    });
  } else {
    // Create new subscription record
    const { error: createError } = await supabaseClient
      .from("user_subscriptions")
      .insert({
        user_id: userId,
        ...subscriptionData
      });

    if (createError) {
      logStep("Error creating user subscription", { error: createError });
      return new Response("Failed to create subscription", { status: 500 });
    }

    logStep("Created new subscription", { 
      userId, 
      tier: subscriptionTier, 
      subscriptionId: subscription.id 
    });
  }

  // Usage tracking is now handled automatically by database triggers
  // No manual refresh needed when subscription changes
  logStep("Subscription processing completed successfully", { 
    userId, 
    tier: subscriptionTier, 
    subscriptionId: subscription.id,
    status: subscription.status 
  });

  return null; // Success - no error response
};

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Validate environment at startup
    validateEnvironment();

    const signature = req.headers.get("stripe-signature");
    const body = await req.text();
    
    if (!signature) {
      logStep("Webhook rejected: Missing signature");
      return new Response(
        JSON.stringify({ error: "Missing Stripe signature header" }), 
        { 
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    // Initialize Stripe
    const stripe = createStripeClient();

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Verify webhook signature with timestamp tolerance (5 minutes default)
    const isProduction = Deno.env.get("NODE_ENV") === "production";
    const webhookSecretKey = isProduction ? "STRIPE_LIVE_WEBHOOK_SECRET_SUBSCRIPTION" : "STRIPE_TEST_WEBHOOK_SECRET_SUBSCRIPTION";
    const webhookSecret = Deno.env.get(webhookSecretKey)!;
    const tolerance = 300; // 5 minutes in seconds

    let event: Stripe.Event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret, tolerance);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      logStep("Webhook signature verification failed", { error: errorMessage });
      
      // Provide more specific error messages for debugging
      if (errorMessage.includes('timestamp')) {
        return new Response(
          JSON.stringify({ error: "Webhook timestamp too old (possible replay attack)" }), 
          { 
            status: 400,
            headers: { "Content-Type": "application/json" }
          }
        );
      }
      
      return new Response(
        JSON.stringify({ error: "Invalid webhook signature" }), 
        { 
          status: 400,
          headers: { "Content-Type": "application/json" }
        }
      );
    }

    logStep("Webhook signature verified", { type: event.type, id: event.id });

    // Check for duplicate events
    const alreadyProcessed = await isEventProcessed(supabaseClient, event.id);
    if (alreadyProcessed) {
      logStep("Event already processed, skipping", { eventId: event.id, type: event.type });
      return new Response("Event already processed", { status: 200 });
    }

    logStep("Processing webhook event", { type: event.type, id: event.id });

    switch (event.type) {

      
      
      case "invoice.paid": {
        const invoice = event.data.object as Stripe.Invoice;
        
        logStep("Processing invoice paid", { 
          invoiceId: invoice.id, 
          subscriptionId: invoice.subscription,
          customerId: invoice.customer 
        });

        // Check if this is a PAYG purchase (not a subscription)
        let isPaygPurchase = false;

        // First, check if any line item is a PAYG purchase
        for (const lineItem of invoice.lines.data) {
          const priceId = lineItem.pricing?.price_details?.price;
          if (priceId && isPaygPriceId(priceId)) {
            isPaygPurchase = true;
            logStep("Detected PAYG purchase", { priceId, invoiceId: invoice.id });
            break;
          }
        }

        let result: Response | null;

        if (isPaygPurchase) {
          // Handle PAYG credit purchase
          result = await handlePaygPurchase(invoice, stripe, supabaseClient);
          if (result) return result; // Early return if error occurred
        } else {
          // Handle subscription upgrade/renewal
          result = await handleSubscriptionUpgrade(invoice, stripe, supabaseClient);
          if (result) return result; // Early return if error occurred
        }
        
        break;
      }

      default:
        logStep("Unhandled event type", { type: event.type });
        return new Response("Unhandled event type", { status: 200 });
    }

    // Mark event as processed
    await markEventAsProcessed(supabaseClient, event.id, event.type);

    return new Response("Webhook processed successfully", { status: 200 });

  } catch (error) {
    logStep("Error in subscription webhook", { error: error.message });
    console.error("Error in subscription webhook:", error);
    return new Response("Internal server error", { status: 500 });
  }
}); 