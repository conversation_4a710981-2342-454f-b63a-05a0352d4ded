import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";
import { createStripeClient } from "../_shared/stripe.ts";
import { ensureUserSubscription } from "../_shared/supabase.ts";
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type"
};
const logStep = (step, details)=>{
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[STRIPE-WEBHOOK-SUBSCRIPTION] ${step}${detailsStr}`);
};
// Validate required environment variables at startup
const validateEnvironment = ()=>{
  const isProduction = Deno.env.get("NODE_ENV") === "production";
  const required = [
    'SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY'
  ];
  // Check for environment-specific Stripe key and webhook secret
  const stripeKey = isProduction ? 'STRIPE_LIVE_SECRET_KEY' : 'STRIPE_TEST_SECRET_KEY';
  const webhookSecret = isProduction ? 'STRIPE_LIVE_WEBHOOK_SECRET_SUBSCRIPTION' : 'STRIPE_TEST_WEBHOOK_SECRET_SUBSCRIPTION';
  required.push(stripeKey, webhookSecret);
  const missing = required.filter((key)=>!Deno.env.get(key));
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
};
// Check if event has already been processed to prevent duplicates
const isEventProcessed = async (supabaseClient, eventId)=>{
  try {
    const { data } = await supabaseClient.from('processed_webhook_events').select('id').eq('stripe_event_id', eventId).single();
    return !!data;
  } catch (error) {
    // If table doesn't exist or other error, assume not processed
    return false;
  }
};
// Mark event as processed
const markEventAsProcessed = async (supabaseClient, eventId, eventType)=>{
  try {
    await supabaseClient.from('processed_webhook_events').insert({
      stripe_event_id: eventId,
      event_type: eventType,
      processed_at: new Date().toISOString()
    });
  } catch (error) {
    // Log error but don't fail the webhook - the processing was successful
    console.warn('Failed to mark event as processed:', error);
  }
};
Deno.serve(async (req)=>{
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders
    });
  }
  try {
    // Validate environment at startup
    validateEnvironment();
    const signature = req.headers.get("stripe-signature");
    const body = await req.text();
    if (!signature) {
      logStep("Webhook rejected: Missing signature");
      return new Response(JSON.stringify({
        error: "Missing Stripe signature header"
      }), {
        status: 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
    // Initialize Stripe
    const stripe = createStripeClient();
    // Create Supabase client
    const supabaseClient = createClient(Deno.env.get("SUPABASE_URL") ?? "", Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "");
    // Verify webhook signature with timestamp tolerance (5 minutes default)
    const isProduction = Deno.env.get("NODE_ENV") === "production";
    const webhookSecretKey = isProduction ? "STRIPE_LIVE_WEBHOOK_SECRET_SUBSCRIPTION" : "STRIPE_TEST_WEBHOOK_SECRET_SUBSCRIPTION";
    const webhookSecret = Deno.env.get(webhookSecretKey);
    const tolerance = 300; // 5 minutes in seconds
    let event;
    try {
      event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret, tolerance);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      logStep("Webhook signature verification failed", {
        error: errorMessage
      });
      // Provide more specific error messages for debugging
      if (errorMessage.includes('timestamp')) {
        return new Response(JSON.stringify({
          error: "Webhook timestamp too old (possible replay attack)"
        }), {
          status: 400,
          headers: {
            "Content-Type": "application/json"
          }
        });
      }
      return new Response(JSON.stringify({
        error: "Invalid webhook signature"
      }), {
        status: 400,
        headers: {
          "Content-Type": "application/json"
        }
      });
    }
    logStep("Webhook signature verified", {
      type: event.type,
      id: event.id
    });
    // Check for duplicate events
    const alreadyProcessed = await isEventProcessed(supabaseClient, event.id);
    if (alreadyProcessed) {
      logStep("Event already processed, skipping", {
        eventId: event.id,
        type: event.type
      });
      return new Response("Event already processed", {
        status: 200
      });
    }
    logStep("Processing webhook event", {
      type: event.type,
      id: event.id
    });
    switch(event.type){
      case "checkout.session.completed":
        {
          const session = event.data.object;
          
          if (session.mode === "subscription") {
            // Handle subscription checkout session
            logStep("Processing completed subscription checkout session", {
              sessionId: session.id,
              customerId: session.customer,
              subscriptionId: session.subscription
            });
            const userId = session.metadata?.supabase_user_id;
            const targetTier = session.metadata?.subscription_tier;
            const billingCycle = session.metadata?.billing_cycle || 'monthly';
            if (!userId || !targetTier) {
              console.error("Missing required metadata in subscription checkout session:", session.metadata);
              return new Response("Missing metadata", {
                status: 400
              });
            }
            // Get the full subscription details
            const subscription = await stripe.subscriptions.retrieve(session.subscription);
            
            // Ensure user has a subscription record before updating
            await ensureUserSubscription(userId);
            
            // Update our database with the new subscription
            await supabaseClient.rpc("update_subscription_tier_limits", {
              user_uuid: userId,
              new_tier: targetTier
            });
            await supabaseClient.from("user_subscriptions").update({
              stripe_subscription_id: subscription.id,
              stripe_customer_id: subscription.customer,
              billing_cycle: billingCycle,
              subscription_start_date: new Date(subscription.current_period_start * 1000).toISOString(),
              subscription_end_date: new Date(subscription.current_period_end * 1000).toISOString(),
              subscription_status: subscription.status,
              updated_at: new Date().toISOString()
            }).eq("user_id", userId);
            logStep("Subscription activated successfully", {
              userId,
              tier: targetTier,
              subscriptionId: subscription.id
            });
          } else if (session.mode === "payment" && session.metadata?.type === "payg_credits") {
            // Handle PAYG credit purchase checkout session
            logStep("Processing completed PAYG credit purchase", {
              sessionId: session.id,
              customerId: session.customer,
              amount: session.amount_total
            });
            
            const userId = session.metadata.user_id;
            const credits = parseInt(session.metadata.credits || "0");
            const packageType = session.metadata.package_type;
            
            if (!userId || !credits) {
              console.error("Missing required metadata in PAYG checkout session:", session.metadata);
              return new Response("Missing metadata", {
                status: 400
              });
            }

            logStep("Processing pay-as-you-go credit purchase", {
              userId,
              credits,
              packageType,
              amountPaid: session.amount_total
            });

            try {
              // Ensure user has a subscription record before adding credits
              await ensureUserSubscription(userId);
              
              // Add credits to user's subscription using the centralized function
              const { data: purchaseId, error: addCreditsError } = await supabaseClient
                .rpc("add_payg_credits", {
                  user_uuid: userId,
                  credits_amount: credits,
                  price_paid_cents: session.amount_total || 0,
                  stripe_payment_intent_id: session.payment_intent as string
                });

              if (addCreditsError) {
                console.error("Error adding pay-as-you-go credits:", addCreditsError);
                return new Response("Failed to add credits", {
                  status: 500
                });
              }

              logStep("Successfully added PAYG credits", {
                userId,
                credits,
                purchaseId,
                sessionId: session.id
              });
              
            } catch (error) {
              console.error("Error processing pay-as-you-go credit purchase:", error);
              return new Response("Processing error", {
                status: 500
              });
            }
          }
          break;
        }
      case "customer.subscription.created":
        {
          const subscription = event.data.object;
          logStep("Processing subscription created", {
            subscriptionId: subscription.id
          });
          const userId = subscription.metadata?.supabase_user_id;
          const targetTier = subscription.metadata?.subscription_tier;
          const billingCycle = subscription.metadata?.billing_cycle || 'monthly';
          if (!userId || !targetTier) {
            console.error("Missing required metadata in subscription:", subscription.metadata);
            return new Response("Missing metadata", {
              status: 400
            });
          }
          // Ensure user has a subscription record before updating
          await ensureUserSubscription(userId);
          
          // Update our database
          await supabaseClient.rpc("update_subscription_tier_limits", {
            user_uuid: userId,
            new_tier: targetTier
          });
          await supabaseClient.from("user_subscriptions").update({
            stripe_subscription_id: subscription.id,
            stripe_customer_id: subscription.customer,
            billing_cycle: billingCycle,
            subscription_start_date: new Date(subscription.current_period_start * 1000).toISOString(),
            subscription_end_date: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_status: subscription.status,
            updated_at: new Date().toISOString()
          }).eq("user_id", userId);
          logStep("Subscription created and updated in database", {
            userId,
            tier: targetTier
          });
          break;
        }
      case "customer.subscription.updated":
        {
          const subscription = event.data.object;
          logStep("Processing subscription updated", {
            subscriptionId: subscription.id,
            status: subscription.status
          });
          // Find the user by subscription ID
          const { data: userSub } = await supabaseClient.from("user_subscriptions").select("user_id").eq("stripe_subscription_id", subscription.id).single();
          if (!userSub) {
            console.error("Could not find user subscription for Stripe subscription:", subscription.id);
            return new Response("User subscription not found", {
              status: 404
            });
          }
          // Update subscription status and dates
          await supabaseClient.from("user_subscriptions").update({
            subscription_end_date: new Date(subscription.current_period_end * 1000).toISOString(),
            subscription_status: subscription.status,
            updated_at: new Date().toISOString()
          }).eq("stripe_subscription_id", subscription.id);
          logStep("Subscription updated in database", {
            subscriptionId: subscription.id
          });
          break;
        }
      case "customer.subscription.deleted":
        {
          const subscription = event.data.object;
          logStep("Processing subscription cancellation", {
            subscriptionId: subscription.id
          });
          // Find the user by subscription ID
          const { data: userSub } = await supabaseClient.from("user_subscriptions").select("user_id").eq("stripe_subscription_id", subscription.id).single();
          if (!userSub) {
            console.error("Could not find user subscription for Stripe subscription:", subscription.id);
            return new Response("User subscription not found", {
              status: 404
            });
          }
          // Downgrade to free tier
          await supabaseClient.rpc("update_subscription_tier_limits", {
            user_uuid: userSub.user_id,
            new_tier: "Sample the Table"
          });
          await supabaseClient.from("user_subscriptions").update({
            stripe_subscription_id: null,
            subscription_status: 'canceled',
            subscription_end_date: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }).eq("stripe_subscription_id", subscription.id);
          logStep("Subscription canceled and user downgraded to free tier", {
            userId: userSub.user_id
          });
          break;
        }
      case "invoice.payment_failed":
        {
          const invoice = event.data.object;
          if (invoice.subscription) {
            logStep("Processing failed payment", {
              subscriptionId: invoice.subscription
            });
            // Find the user by subscription ID
            const { data: userSub } = await supabaseClient.from("user_subscriptions").select("user_id").eq("stripe_subscription_id", invoice.subscription).single();
            if (userSub) {
              // Update subscription status
              await supabaseClient.from("user_subscriptions").update({
                subscription_status: 'past_due',
                updated_at: new Date().toISOString()
              }).eq("stripe_subscription_id", invoice.subscription);
              logStep("Subscription marked as past due", {
                userId: userSub.user_id
              });
            }
          }
          break;
        }
      case "invoice.payment_succeeded":
        {
          const invoice = event.data.object;
          if (invoice.subscription) {
            logStep("Processing successful payment", {
              subscriptionId: invoice.subscription
            });
            // Find the user by subscription ID
            const { data: userSub } = await supabaseClient.from("user_subscriptions").select("user_id").eq("stripe_subscription_id", invoice.subscription).single();
            if (userSub) {
              // Update subscription status to active
              await supabaseClient.from("user_subscriptions").update({
                subscription_status: 'active',
                updated_at: new Date().toISOString()
              }).eq("stripe_subscription_id", invoice.subscription);
              logStep("Subscription reactivated after successful payment", {
                userId: userSub.user_id
              });
            }
          }
          break;
        }
      default:
        logStep("Unhandled event type", {
          type: event.type
        });
        break;
    }
    // Mark event as processed
    await markEventAsProcessed(supabaseClient, event.id, event.type);
    return new Response("Webhook processed successfully", {
      status: 200
    });
  } catch (error) {
    logStep("Error in subscription webhook", {
      error: error.message
    });
    console.error("Error in subscription webhook:", error);
    return new Response("Internal server error", {
      status: 500
    });
  }
});
