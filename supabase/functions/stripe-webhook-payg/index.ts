import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2";
import Stripe from "https://esm.sh/stripe@14.21.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[STRIPE-WEBHOOK-PAYG] ${step}${detailsStr}`);
};

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const signature = req.headers.get("stripe-signature");
    const body = await req.text();
    
    if (!signature) {
      return new Response("No signature", { status: 400 });
    }

    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") ?? "", {
      apiVersion: "2023-10-16",
    });

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? ""
    );

    // Verify webhook signature
    const webhookSecret = Deno.env.get("STRIPE_WEBHOOK_SECRET");
    if (!webhookSecret) {
      throw new Error("STRIPE_WEBHOOK_SECRET is not set");
    }

    let event: Stripe.Event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error("Webhook signature verification failed:", err);
      return new Response("Invalid signature", { status: 400 });
    }

    console.log("Processing webhook event:", event.type);

    // Handle checkout session completed for pay-as-you-go credits
    if (event.type === "checkout.session.completed") {
      const session = event.data.object as Stripe.Checkout.Session;
      
      // Check if this is a pay-as-you-go credit purchase
      if (session.metadata?.type === "payg_credits" && session.mode === "payment") {
        const userId = session.metadata.user_id;
        const credits = parseInt(session.metadata.credits || "0");
        const packageType = session.metadata.package_type;
        
        if (!userId || !credits) {
          console.error("Missing required metadata in checkout session:", session.metadata);
          return new Response("Missing metadata", { status: 400 });
        }

        console.log(`Processing pay-as-you-go credit purchase for user ${userId}: ${credits} credits`);

        try {
          // Add credits to user's subscription using the centralized function
          const { data: purchaseId, error: addCreditsError } = await supabaseClient
            .rpc("add_payg_credits", {
              user_uuid: userId,
              credits_amount: credits,
              price_paid_cents: session.amount_total || 0,
              stripe_payment_intent_id: session.payment_intent as string
            });

          if (addCreditsError) {
            console.error("Error adding pay-as-you-go credits:", addCreditsError);
            return new Response("Failed to add credits", { status: 500 });
          }

          console.log(`Successfully added ${credits} credits to user ${userId}, purchase ID: ${purchaseId}`);
          
        } catch (error) {
          console.error("Error processing pay-as-you-go credit purchase:", error);
          return new Response("Processing error", { status: 500 });
        }
      }
    }

    return new Response("Webhook processed successfully", { status: 200 });

  } catch (error) {
    console.error("Error in stripe webhook:", error);
    return new Response("Internal server error", { status: 500 });
  }
}); 