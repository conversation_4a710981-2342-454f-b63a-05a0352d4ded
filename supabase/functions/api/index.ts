// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "@supabase/functions-js/edge-runtime.d.ts";
import { Hono } from "@hono/hono";
import { cors } from "@hono/hono/cors";
import { nanoid } from "nanoid";
import {
  createSupabaseClient,
  createSupabaseClientFromContext,
  getAuthenticatedUser,
  ensureUserSubscription,
} from "../_shared/supabase.ts";
import { logStep } from "../_shared/logger.ts";
import { createStripeClient, Stripe } from "../_shared/stripe.ts";
import {
  getStripePriceId,
  getPaygPriceId,
  PAYG_PACKAGES,
} from "../_shared/pricing.ts";
import { sendEmail } from "../_shared/postmark.ts";
import { getEmailTemplate } from "../_shared/email-templates.ts";
// import { createClient } from "jsr:@supabase/supabase-js@2";

const app = new Hono().basePath("/api");

// Add CORS middleware
app.use(
  "/*",
  cors({
    origin: "*",
    allowHeaders: ["authorization", "x-client-info", "apikey", "content-type"],
  })
);

// Constants and configurations
const OPENAI_API_KEY = Deno.env.get("OPENAI_API_KEY");

// Rate limiting for anonymous user creation
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const MAX_ANONYMOUS_USERS_PER_IP = 3; // Max 3 anonymous users per IP per minute
const rateTracker = new Map<string, { count: number; resetTime: number }>();

// Rate limiting helper function
function checkRateLimit(ip: string): { allowed: boolean; retryAfter?: number } {
  const now = Date.now();
  const tracker = rateTracker.get(ip);

  // Clean up expired entries
  if (tracker && now > tracker.resetTime) {
    rateTracker.delete(ip);
  }

  const currentTracker = rateTracker.get(ip);
  
  if (!currentTracker) {
    // First request from this IP
    rateTracker.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return { allowed: true };
  }

  if (currentTracker.count >= MAX_ANONYMOUS_USERS_PER_IP) {
    const retryAfter = Math.ceil((currentTracker.resetTime - now) / 1000);
    return { allowed: false, retryAfter };
  }

  // Increment counter
  currentTracker.count++;
  return { allowed: true };
}

// PAYG_PACKAGES is now imported from pricing.ts

// Development mode random text responses
const RANDOM_RESPONSES = [
  "Ah, darling, you've got that look in your eyes - the same one I had when I first walked into this godforsaken city. Tell me, what brings you to my table tonight?",
  "The smoke from my cigarette curls like secrets in the dim light. I've seen things in this town that would make a saint reach for the bottle. What's your poison?",
  "Listen here, sweetheart, in this business you learn to read people like yesterday's newspaper. And you? You're front page material.",
  "The rain's been falling on this city for three days straight, washing away the sins but never quite getting them clean. What's eating at you?",
  "I've been in this racket long enough to know when someone's carrying more than they can handle. Spill it, and maybe we can both sleep better tonight.",
  "The neon lights outside paint everything in shades of desperation. But you, you've got something different about you. What's your story?",
  "In this town, everybody's got an angle, everybody's working an angle. The question is - what's yours?",
  "The jazz from the club downstairs seeps through the floorboards like a heartbeat. It's the only honest thing left in this city. Now, what can you tell me?",
];

function generateRandomResponse(): string {
  return RANDOM_RESPONSES[Math.floor(Math.random() * RANDOM_RESPONSES.length)];
}

function isDevelopmentMode(origin: string | undefined): boolean {
  if (!origin) return false;
  return (
    origin.includes("localhost") ||
    origin.includes("127.0.0.1") ||
    origin.includes("0.0.0.0")
  );
}

// ================================
// AUTH ROUTES (/auth/*)
// ================================

app.post("/auth/anonymous-signin", async (c) => {
  try {
    logStep("api", "Starting anonymous signin request");

    // Get client IP for rate limiting
    const clientIP = c.req.header("x-forwarded-for")?.split(",")[0]?.trim() || 
                     c.req.header("x-real-ip") || 
                     c.req.header("cf-connecting-ip") || 
                     "unknown";

    // Check rate limit
    const rateCheck = checkRateLimit(clientIP);
    if (!rateCheck.allowed) {
      logStep("api", "Anonymous signin rate limited", { 
        clientIP, 
        retryAfter: rateCheck.retryAfter 
      });
      return c.json(
        {
          error: "Rate limit exceeded",
          details: "Too many anonymous users created from this location. Please try again later.",
          retryAfter: rateCheck.retryAfter,
        },
        429,
        {
          "Retry-After": rateCheck.retryAfter?.toString() || "60",
        }
      );
    }

    logStep("api", "Rate limit check passed", { clientIP });

    // Create anonymous user using Supabase Admin API
    const supabaseClient = createSupabaseClient();
    
    // Use the proper anonymous sign-in method instead of admin.createUser
    const { data, error } = await supabaseClient.auth.signInAnonymously();
    
    // If successful, update user metadata
    if (!error && data.user) {
      try {
        await supabaseClient.auth.admin.updateUserById(data.user.id, {
          user_metadata: {
            is_anonymous: true,
            anonymous_created_at: new Date().toISOString(),
          },
        });
      } catch (metadataError) {
        logStep("api", "Failed to update anonymous user metadata", { 
          userId: data.user.id, 
          error: metadataError 
        });
        // Continue anyway - the user is created successfully
      }
    }

    if (error) {
      logStep("api", "Anonymous signin failed", { 
        error: error.message,
        errorCode: error.code || "unknown",
        clientIP
      });
      
      // Provide more specific error messages based on error type
      let errorMessage = "Failed to create anonymous user";
      let statusCode = 500;
      
      if (error.message?.includes("rate limit") || error.message?.includes("too many")) {
        errorMessage = "Service temporarily unavailable. Please try again in a few minutes.";
        statusCode = 503;
      } else if (error.message?.includes("network") || error.message?.includes("timeout")) {
        errorMessage = "Network error. Please check your connection and try again.";
        statusCode = 503;
      } else if (error.message?.includes("database") || error.message?.includes("connection")) {
        errorMessage = "Service temporarily unavailable. Please try again later.";
        statusCode = 503;
      }
      
      return c.json(
        {
          error: errorMessage,
          details: error.message,
          code: error.code || "auth_error",
        },
        statusCode
      );
    }

    logStep("api", "Anonymous user created successfully", { 
      userId: data.user.id,
      clientIP,
      userAgent: c.req.header("user-agent")?.substring(0, 100),
      createdAt: data.user.created_at,
      isAnonymous: data.user.user_metadata?.is_anonymous
    });

    // Return minimal user data for security
    return c.json({
      success: true,
      user: {
        id: data.user.id,
        created_at: data.user.created_at,
        is_anonymous: true,
        user_metadata: {
          is_anonymous: true,
          anonymous_created_at: data.user.user_metadata?.anonymous_created_at
        }
      },
      session: data.session ? {
        access_token: data.session.access_token,
        token_type: data.session.token_type,
        expires_in: data.session.expires_in,
        expires_at: data.session.expires_at,
        refresh_token: data.session.refresh_token
      } : null,
    });
  } catch (error) {
    logStep("api", "Internal server error in anonymous signin", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

app.post("/auth/upgrade-to-email", async (c) => {
  try {
    logStep("api", "Starting email upgrade request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { email } = await c.req.json();

    if (!email) {
      return c.json({ error: "Email is required" }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400);
    }

    // Check if user is anonymous
    const supabaseClient = createSupabaseClient();

    // Check if email is already in use by another user
    const { data: existingUser, error: existingUserError } = await supabaseClient.auth.admin.listUsers();
    
    if (existingUserError) {
      logStep("api", "Error checking existing users", { 
        error: existingUserError.message 
      });
      return c.json({ 
        error: "Failed to verify email availability" 
      }, 500);
    }

    const emailInUse = existingUser.users.some(
      (u) => u.email?.toLowerCase() === email.toLowerCase() && u.id !== user.id
    );

    if (emailInUse) {
      logStep("api", "Email already in use", { 
        email: email.toLowerCase(),
        userId: user.id 
      });
      return c.json({ 
        error: "Email address is already in use by another account" 
      }, 409);
    }
    const { data: isAnonResult, error: isAnonError } = await supabaseClient
      .rpc('is_anonymous_user', { user_uuid: user.id });

    if (isAnonError) {
      logStep("api", "Error checking if user is anonymous", { 
        error: isAnonError.message 
      });
      return c.json({ 
        error: "Failed to verify user status" 
      }, 500);
    }

    if (!isAnonResult) {
      return c.json({ 
        error: "User is not anonymous" 
      }, 400);
    }

    // Use Supabase Admin API to update user email
    const { data: updateData, error: updateError } = 
      await supabaseClient.auth.admin.updateUserById(user.id, {
        email: email,
        email_confirm: true, // Skip email verification - allow immediate access
        user_metadata: {
          ...user.user_metadata,
          is_anonymous: false,
          email_upgraded_at: new Date().toISOString(),
        },
      });

    if (updateError) {
      logStep("api", "Email upgrade failed", { 
        error: updateError.message,
        errorCode: updateError.code || "unknown",
        userId: user.id,
        email: email.toLowerCase()
      });
      
      // Provide more specific error messages
      let errorMessage = "Failed to upgrade account";
      let statusCode = 500;
      
      if (updateError.message?.includes("email") && updateError.message?.includes("exists")) {
        errorMessage = "Email address is already in use by another account";
        statusCode = 409;
      } else if (updateError.message?.includes("invalid") && updateError.message?.includes("email")) {
        errorMessage = "Invalid email address format";
        statusCode = 400;
      } else if (updateError.message?.includes("rate limit") || updateError.message?.includes("too many")) {
        errorMessage = "Too many requests. Please wait a moment and try again.";
        statusCode = 429;
      } else if (updateError.message?.includes("network") || updateError.message?.includes("timeout")) {
        errorMessage = "Network error. Please check your connection and try again.";
        statusCode = 503;
      }
      
      return c.json({
        error: errorMessage,
        details: updateError.message,
        code: updateError.code || "upgrade_error",
      }, statusCode);
    }

    logStep("api", "Email upgrade successful", { 
      userId: user.id,
      newEmail: email.toLowerCase(),
      previouslyAnonymous: true,
      upgradeTimestamp: new Date().toISOString(),
      userAgent: c.req.header("user-agent")?.substring(0, 100)
    });

    // Return minimal user data for security  
    return c.json({
      success: true,
      message: "Email upgrade successful. You can continue your conversations immediately!",
      user: {
        id: updateData.user.id,
        email: updateData.user.email,
        created_at: updateData.user.created_at,
        updated_at: updateData.user.updated_at,
        is_anonymous: false,
        user_metadata: {
          is_anonymous: false,
          email_upgraded_at: updateData.user.user_metadata?.email_upgraded_at
        }
      },
    });
  } catch (error) {
    logStep("api", "Internal server error in email upgrade", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// EMAIL ROUTES (/email/*)
// ================================

app.post("/email/send", async (c) => {
  try {
    logStep("api", "Starting email send request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(
        {
          error: "User not authenticated.",
          details: authError,
        },
        401
      );
    }

    const {
      template_type,
      to_email,
      template_data,
      from_email = "<EMAIL>",
      reply_to,
      cc,
      bcc,
      tag,
    } = await c.req.json();

    if (!template_type || !to_email || !template_data) {
      return c.json(
        {
          error:
            "Missing required fields: template_type, to_email, and template_data are required.",
        },
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to_email)) {
      return c.json({ error: "Invalid recipient email format" }, 400);
    }

    // Get email template
    let emailTemplate;
    try {
      emailTemplate = getEmailTemplate(
        template_type as any,
        template_data as any
      );
    } catch (templateError) {
      return c.json(
        {
          error: "Invalid template type or data",
          details: (templateError as Error).message,
        },
        400
      );
    }

    // Send email using Postmark
    try {
      const result = await sendEmail({
        from: from_email,
        to: to_email,
        subject: emailTemplate.subject,
        htmlBody: emailTemplate.htmlBody,
        textBody: emailTemplate.textBody,
        replyTo: reply_to,
        cc,
        bcc,
        tag,
      });

      logStep("api", "Email sent successfully", {
        to: to_email,
        template: template_type,
        messageId: result.MessageID,
      });

      return c.json({
        success: true,
        message: "Email sent successfully",
        messageId: result.MessageID,
      });
    } catch (emailError) {
      logStep("api", "Failed to send email", {
        error: (emailError as Error).message,
        to: to_email,
        template: template_type,
      });

      return c.json(
        {
          error: "Failed to send email",
          details: (emailError as Error).message,
        },
        500
      );
    }
  } catch (error) {
    logStep("api", "Internal server error in email send", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// Custom email route that allows sending emails with custom content
app.post("/email/send-custom", async (c) => {
  try {
    logStep("api", "Starting custom email send request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(
        {
          error: "User not authenticated.",
          details: authError,
        },
        401
      );
    }

    // Check if user has admin role
    const supabaseClient = createSupabaseClientFromContext(c);
    const { data: profile, error: profileError } = await supabaseClient
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    if (profileError) {
      return c.json(
        {
          error: "Failed to verify user permissions",
          details: profileError.message,
        },
        500
      );
    }

    // Only allow admins to send custom emails
    if (profile?.role !== "admin") {
      return c.json({ error: "Unauthorized. Admin role required." }, 403);
    }

    const {
      to_email,
      subject,
      html_content,
      text_content,
      from_email = "<EMAIL>",
      reply_to,
      cc,
      bcc,
      tag,
    } = await c.req.json();

    if (!to_email || !subject || (!html_content && !text_content)) {
      return c.json(
        {
          error:
            "Missing required fields: to_email, subject, and either html_content or text_content are required.",
        },
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to_email)) {
      return c.json({ error: "Invalid recipient email format" }, 400);
    }

    // Send email using Postmark
    try {
      const result = await sendEmail({
        from: from_email,
        to: to_email,
        subject: subject,
        htmlBody: html_content,
        textBody: text_content,
        replyTo: reply_to,
        cc,
        bcc,
        tag,
      });

      logStep("api", "Custom email sent successfully", {
        to: to_email,
        subject: subject,
        messageId: result.MessageID,
      });

      return c.json({
        success: true,
        message: "Custom email sent successfully",
        messageId: result.MessageID,
      });
    } catch (emailError) {
      logStep("api", "Failed to send custom email", {
        error: (emailError as Error).message,
        to: to_email,
      });

      return c.json(
        {
          error: "Failed to send custom email",
          details: (emailError as Error).message,
        },
        500
      );
    }
  } catch (error) {
    logStep("api", "Internal server error in custom email send", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// CHAT ROUTES (/chat/*)
// ================================

app.post("/chat", async (c) => {
  try {
    logStep("api", "Starting chat request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(
        {
          error: "User not authenticated.",
          details: authError,
        },
        401
      );
    }

    const {
      character,
      conversation_id: requestConversationId,
      user_chat,
    } = await c.req.json();
    const originalConversationId = requestConversationId;
    let conversation_id = requestConversationId;

    if (!character || !user_chat) {
      return c.json(
        {
          error: "Character and user_chat are required.",
        },
        400
      );
    }

    logStep("api", "User authenticated for chat", { userId: user.id });

    // Ensure user has subscription record and check usage limits
    try {
      const supabaseClient = createSupabaseClientFromContext(c);

      // Ensure user has a subscription record before checking limits
      try {
        await ensureUserSubscription(user.id);
      } catch (subscriptionError) {
        logStep("api", "Failed to ensure user subscription", {
          error: subscriptionError.message,
        });
        return c.json(
          {
            error: "Unable to initialize user subscription. Please try again.",
            details: subscriptionError.message,
          },
          500
        );
      }

      // Always check message limits (no longer tracking conversations)
      const { data: actionResult, error: actionError } =
        await supabaseClient.rpc("can_user_perform_action", {
          user_uuid: user.id,
          action_type: "message",
        });

      if (actionError) {
        logStep("api", "Usage check failed", { error: actionError });
        return c.json(
          {
            error: "Unable to verify usage limits. Please try again.",
            details: actionError.message,
          },
          500
        );
      }

      const result = actionResult[0];
      if (!result.can_perform) {
        logStep("api", "Usage limit exceeded", { reason: result.reason });
        return c.json(
          {
            error: "message_limit_exceeded",
            message: result.reason,
            upgradeRequired: true,
          },
          429
        );
      }

      logStep("api", "Usage limits passed", {
        canPerform: result.can_perform,
        usingPayg: result.using_payg,
      });
    } catch (error) {
      logStep("api", "Usage check error", { error: (error as Error).message });
    }

    const supabaseClient = createSupabaseClient(c.req.header("authorization"));

    // Fetch character data
    const { data: characterData, error: characterError } = await supabaseClient
      .from("characters")
      .select("system_prompt, name")
      .eq("id", character)
      .single();

    if (characterError || !characterData) {
      return c.json(
        {
          error: "Character not found.",
          details: characterError?.message,
        },
        404
      );
    }

    const messages: Array<{ role: string; content: string }> = [];

    if (conversation_id) {
      const { data: conversationData, error: conversationError } =
        await supabaseClient
          .from("conversations")
          .select("id")
          .eq("id", conversation_id)
          .single();

      if (conversationError || !conversationData) {
        return c.json({ error: "Conversation not found." }, 404);
      }

      const { data: conversationMessages, error: messagesError } =
        await supabaseClient
          .from("messages")
          .select("sender_type, content")
          .eq("conversation_id", conversation_id)
          .order("created_at", { ascending: true });

      if (messagesError) {
        return c.json(
          {
            error: "Error fetching messages.",
            details: messagesError.message,
          },
          500
        );
      }

      if (conversationMessages) {
        conversationMessages.forEach((msg) => {
          messages.push({
            role: msg.sender_type === "user" ? "user" : "assistant",
            content: msg.content,
          });
        });
      }
    }

    messages.push({
      role: "user",
      content: user_chat,
    });

    // Check if we're in development mode
    const origin = c.req.header("origin");
    const isDevMode = isDevelopmentMode(origin);

    let aiResponseContent: string;

    if (isDevMode) {
      logStep("api", "Development mode detected - using random response", {
        messageCount: messages.length + 1,
        character: characterData.name,
        origin: origin,
      });

      // Simulate some delay to make it feel more realistic
      await new Promise((resolve) =>
        setTimeout(resolve, 500 + Math.random() * 1000)
      );

      aiResponseContent = generateRandomResponse();
    } else {
      logStep("api", "Calling OpenAI API", {
        messageCount: messages.length + 1,
        character: characterData.name,
      });

      // Call OpenAI
      const response = await fetch(
        "https://api.openai.com/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${OPENAI_API_KEY}`,
          },
          body: JSON.stringify({
            model: "gpt-4o",
            messages: [
              {
                role: "system",
                content: characterData.system_prompt,
              },
              ...messages,
            ],
          }),
        }
      );

      const openAIResponse = await response.json();
      if (!response.ok) {
        logStep("api", "OpenAI API error", { error: openAIResponse });
        return c.json(
          {
            error: "OpenAI API error",
            details: openAIResponse,
          },
          response.status
        );
      }

      aiResponseContent = openAIResponse.choices[0].message.content || "";
    }

    // Create conversation if needed
    if (!conversation_id) {
      const { data: insertedConversation, error: insertConversationError } =
        await supabaseClient
          .from("conversations")
          .insert({
            character_id: character,
            character_name: characterData.name,
            user_id: user.id,
            title: `Chat with ${characterData.name}`,
            last_message_preview: aiResponseContent.slice(0, 100) + "...",
            message_count: 1,
          })
          .select();

      if (insertConversationError) {
        return c.json({ error: insertConversationError.message }, 500);
      }

      conversation_id = insertedConversation[0].id;
    }

    // Save messages
    const { data: userMessageData, error: saveUserMessageError } =
      await supabaseClient
        .from("messages")
        .insert({
          conversation_id: conversation_id,
          sender_type: "user",
          content: user_chat,
        })
        .select("id")
        .single();

    const { data: assistantMessageData, error: saveAssistantMessageError } =
      await supabaseClient
        .from("messages")
        .insert({
          conversation_id: conversation_id,
          sender_type: "character",
          content: aiResponseContent,
        })
        .select("id")
        .single();

    if (saveUserMessageError || saveAssistantMessageError) {
      return c.json(
        {
          error: "Failed to save messages.",
          userError: saveUserMessageError?.message,
          assistantError: saveAssistantMessageError?.message,
        },
        500
      );
    }

    // Usage tracking is now handled automatically by the track_message_usage trigger
    // No manual refresh needed - trigger updates counts when messages are inserted
    logStep(
      "api",
      "Messages saved, trigger will handle usage tracking automatically"
    );

    return c.json({
      conversation_id: conversation_id,
      user_message_id: userMessageData.id,
      assistant_message_id: assistantMessageData.id,
      openai_response: aiResponseContent,
    });
  } catch (error) {
    logStep("api", "Internal server error in chat", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// USAGE ROUTES (/usage/*)
// ================================

app.post("/usage/check-limits", async (c) => {
  try {
    logStep("api", "Starting check-usage-limits");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { action_type } = await c.req.json();

    if (!action_type || action_type !== "message") {
      return c.json(
        {
          error: 'Invalid action_type. Must be "message"',
        },
        400
      );
    }

    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has a subscription record before checking limits
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in usage check", {
        error: subscriptionError.message,
      });
      return c.json(
        {
          error: "Unable to initialize user subscription. Please try again.",
          details: subscriptionError.message,
        },
        500
      );
    }

    const { data: actionResult, error: actionError } = await supabaseClient.rpc(
      "can_user_perform_action",
      {
        user_uuid: user.id,
        action_type: action_type,
      }
    );

    if (actionError) {
      logStep("api", "Error checking user action", { error: actionError });
      return c.json({ error: "Failed to check usage limits" }, 500);
    }

    const result = actionResult[0];

    return c.json({
      can_perform: result.can_perform,
      reason: result.reason,
      using_payg: result.using_payg,
      subscription_info: result.subscription_info,
    });
  } catch (error) {
    logStep("api", "Internal server error in usage check", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// BILLING ROUTES (/billing/*)
// ================================

app.get("/billing/subscription", async (c) => {
  try {
    logStep("api", "Starting check-subscription");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep(
        "api",
        "Failed to ensure user subscription in billing/subscription",
        { error: subscriptionError.message }
      );
      return c.json(
        {
          error: "Unable to initialize user subscription. Please try again.",
          details: subscriptionError.message,
        },
        500
      );
    }

    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select(
        `
        subscription_tier,
        subscription_status,
        subscription_start_date,
        subscription_end_date,
        stripe_subscription_id,
        stripe_customer_id,
        messages_used_this_period,
        monthly_message_limit,
        payg_credits_remaining,
        current_period_start,
        current_period_end
      `
      )
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep(
        "api",
        "Subscription not found after creation in billing/subscription",
        { userId: user.id }
      );
      return c.json({ error: "Subscription record not found" }, 500);
    }

    // Check paid tier status
    const isPaidTier = subscription.subscription_tier !== "Sample the Table";
    const isActiveStatus = subscription.subscription_status === "active";
    const hasValidEndDate =
      subscription.subscription_end_date &&
      new Date(subscription.subscription_end_date) > new Date();

    let subscribed = false;
    let subscriptionEnd: string | null = null;

    if (isPaidTier && subscription.stripe_subscription_id) {
      if (isActiveStatus && hasValidEndDate) {
        subscribed = true;
        subscriptionEnd = subscription.subscription_end_date;
      } else {
        try {
          const stripe = createStripeClient();
          const stripeSubscription = await stripe.subscriptions.retrieve(
            subscription.stripe_subscription_id
          );

          subscribed = stripeSubscription.status === "active";
          subscriptionEnd = subscribed
            ? new Date(
                stripeSubscription.current_period_end * 1000
              ).toISOString()
            : null;

          // Update database with fresh data
          if (
            subscribed !== isActiveStatus ||
            subscriptionEnd !== subscription.subscription_end_date
          ) {
            await supabaseClient
              .from("user_subscriptions")
              .update({
                subscription_status: stripeSubscription.status,
                subscription_end_date: subscriptionEnd,
                updated_at: new Date().toISOString(),
              })
              .eq("user_id", user.id);
          }
        } catch (stripeError) {
          logStep("api", "Stripe verification failed", { error: stripeError });
          subscribed = isActiveStatus && hasValidEndDate;
          subscriptionEnd = subscribed
            ? subscription.subscription_end_date
            : null;
        }
      }
    }

    return c.json({
      subscribed,
      subscription_tier: subscription.subscription_tier,
      subscription_end: subscriptionEnd,
      usage: {
        messages_used: subscription.messages_used_this_period || 0,
        messages_limit: subscription.monthly_message_limit || 0,
        payg_credits_remaining: subscription.payg_credits_remaining || 0,
      },
    });
  } catch (error) {
    logStep("api", "Internal server error in billing subscription", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

app.post("/billing/customer-portal", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const stripe = createStripeClient();
    const customers = await stripe.customers.list({
      email: user.email,
      limit: 1,
    });

    if (customers.data.length === 0) {
      return c.json({ error: "No Stripe customer found for this user" }, 400);
    }

    const customerId = customers.data[0].id;
    const origin = c.req.header("origin") || "http://localhost:3000";
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: `${origin}/account`,
    });

    return c.json({ url: portalSession.url });
  } catch (error) {
    logStep("api", "Error in customer-portal", {
      error: (error as Error).message,
    });
    return c.json({ error: (error as Error).message }, 500);
  }
});

app.post("/billing/purchase-credits", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const { package_type } = await c.req.json();

    if (
      !package_type ||
      !PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES]
    ) {
      return c.json(
        {
          error: 'Invalid package_type. Must be "starter", "writers", or "pro"',
        },
        400
      );
    }

    const selectedPackage =
      PAYG_PACKAGES[package_type as keyof typeof PAYG_PACKAGES];
    const supabaseClient = createSupabaseClientFromContext(c);

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep("api", "Failed to ensure user subscription in purchase-credits", {
        error: subscriptionError.message,
      });
      return c.json(
        {
          error: "Unable to initialize user subscription. Please try again.",
          details: subscriptionError.message,
        },
        500
      );
    }

    // Get subscription record
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep(
        "api",
        "Subscription not found after creation in purchase-credits",
        { userId: user.id }
      );
      return c.json({ error: "Subscription record not found" }, 500);
    }

    // Create or get Stripe customer
    let customerId = subscription.stripe_customer_id;

    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single();

      const stripe = createStripeClient();
      const customer = await stripe.customers.create({
        email: profile?.email || user.email,
        name: profile?.full_name || undefined,
        metadata: { supabase_user_id: user.id },
      });

      customerId = customer.id;

      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id);
    }

    // Create Stripe Checkout session for one-time payment
    const stripe = createStripeClient();
    const baseUrl =
      Deno.env.get("NODE_ENV") === "production"
        ? "https://www.hollywoodtable.com"
        : "http://localhost:8080";

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: {
              name: `${selectedPackage.name} Credit Package`,
              description: selectedPackage.description,
            },
            unit_amount: selectedPackage.price_cents,
          },
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: `${baseUrl}/pricing?payg_success=true&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${baseUrl}/pricing?payg_canceled=true`,
      metadata: {
        user_id: user.id,
        package_type: package_type,
        credits: selectedPackage.credits.toString(),
        type: "payg_credits",
      },
      billing_address_collection: "auto",
      customer_update: { address: "auto", name: "auto" },
    });

    return c.json({
      success: true,
      checkout_url: session.url,
      session_id: session.id,
      package_info: {
        type: package_type,
        credits: selectedPackage.credits,
        price_cents: selectedPackage.price_cents,
        name: selectedPackage.name,
      },
    });
  } catch (error) {
    logStep("api", "Error in purchase-credits", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

app.post("/billing/manage-subscription", async (c) => {
  try {
    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json({ error: "Unauthorized" }, 401);
    }

    const {
      target_tier: targetTier,
      billing_cycle: rawCycle,
      action,
    } = await c.req.json();
    const billingCycle = rawCycle === "annual" ? "annual" : "monthly";

    // Handle cancel_keep_credits action
    if (action === "cancel_keep_credits") {
      logStep(
        "api",
        "Processing cancel subscription (move to free tier, keep usage)",
        { userId: user.id }
      );

      const supabaseClient = createSupabaseClient();

      // Fetch subscription record first to get stripe_subscription_id
      const { data: subscription, error: subError } = await supabaseClient
        .from("user_subscriptions")
        .select("*")
        .eq("user_id", user.id)
        .single();

      if (subError || !subscription) {
        return c.json({ error: "No active subscription found" }, 404);
      }

      if (subscription.subscription_tier === "Sample the Table") {
        return c.json({ error: "Already on free tier" }, 400);
      }

      const _stripe = createStripeClient();

      // Cancel the Stripe subscription at period end if it exists
      if (subscription.stripe_subscription_id) {
        await _stripe.subscriptions.update(
          subscription.stripe_subscription_id,
          {
            cancel_at_period_end: true,
          }
        );

        logStep("api", "Stripe subscription set to cancel at period end", {
          subscriptionId: subscription.stripe_subscription_id,
        });
      }

      // Use the new database function to move to free tier while keeping usage
      const { data: cancelResult, error: cancelError } =
        await supabaseClient.rpc("cancel_subscription_to_free_tier", {
          user_uuid: user.id,
        });

      if (cancelError) {
        logStep("api", "Error cancelling subscription", { error: cancelError });
        return c.json(
          {
            error: "Failed to cancel subscription",
            details: cancelError.message,
          },
          500
        );
      }

      const result = cancelResult[0];
      if (!result.success) {
        return c.json({ error: result.message }, 400);
      }

      return c.json({
        success: true,
        message:
          "Subscription cancelled. You've been moved to the free tier but will keep your current usage until your next billing cycle.",
        previous_tier: result.previous_tier,
        current_tier: result.new_tier,
        effective_date: subscription.subscription_end_date,
        usage_kept: result.usage_kept,
        action: "cancel_keep_credits",
      });
    }

    // Validate target tier for tier change operations
    const SUBSCRIPTION_TIERS = [
      "Sample the Table",
      "Reserved Seat",
      "VIP Table",
    ] as const;
    if (!targetTier || !SUBSCRIPTION_TIERS.includes(targetTier)) {
      return c.json(
        {
          error: `Invalid target_tier. Must be one of ${SUBSCRIPTION_TIERS.join(
            ", "
          )}`,
        },
        400
      );
    }

    logStep("api", "Processing subscription change", {
      userId: user.id,
      targetTier,
      billingCycle,
    });

    const supabaseClient = createSupabaseClient();

    // Ensure user has subscription record
    try {
      await ensureUserSubscription(user.id);
    } catch (subscriptionError) {
      logStep(
        "api",
        "Failed to ensure user subscription in manage-subscription",
        { error: subscriptionError.message }
      );
      return c.json(
        {
          error: "Unable to initialize user subscription. Please try again.",
          details: subscriptionError.message,
        },
        500
      );
    }

    // Fetch subscription record
    let { data: subscription, error: subError } = await supabaseClient
      .from("user_subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .single();

    if (subError || !subscription) {
      logStep("api", "Subscription not found after creation", {
        userId: user.id,
      });
      return c.json({ error: "Subscription record not found" }, 500);
    }

    const currentTier = subscription.subscription_tier;
    const currentIndex = SUBSCRIPTION_TIERS.indexOf(currentTier as any);
    const targetIndex = SUBSCRIPTION_TIERS.indexOf(targetTier);

    if (currentTier === targetTier) {
      return c.json({ error: "Already on requested tier" }, 400);
    }

    // Resolve Stripe customer
    let customerId = subscription.stripe_customer_id;
    const _stripe = createStripeClient();
    //  const _stripe =  new Stripe(Deno.env.get("STRIPE_TEST_SECRET_KEY") ?? "", {
    //   apiVersion: "2025-05-28.basil",
    // });

    if (!customerId) {
      const { data: profile } = await supabaseClient
        .from("profiles")
        .select("email, full_name")
        .eq("id", user.id)
        .single();

      const customer = await _stripe.customers.create({
        email: profile?.email || user.email,
        name: profile?.full_name,
        metadata: { supabase_user_id: user.id },
      });

      customerId = customer.id;
      await supabaseClient
        .from("user_subscriptions")
        .update({ stripe_customer_id: customerId })
        .eq("user_id", user.id);
    }

    // Determine upgrade or downgrade
    if (targetIndex > currentIndex) {
      // UPGRADE LOGIC
      logStep("api", "Processing upgrade", {
        userId: user.id,
        from: currentTier,
        to: targetTier,
      });

      const priceId = getStripePriceId(targetTier, billingCycle);
      if (!priceId) {
        return c.json({ error: "Price ID missing for upgrade" }, 500);
      }

      if (subscription.stripe_subscription_id) {
        // Modify existing subscription
        const stripeSubscription = await _stripe.subscriptions.retrieve(
          subscription.stripe_subscription_id
        );
        const updatedSubscription = await _stripe.subscriptions.update(
          subscription.stripe_subscription_id,
          {
            items: [
              { id: stripeSubscription.items.data[0].id, price: priceId },
            ],
            proration_behavior: "create_prorations",
          }
        );

        logStep("api", "Updated subscription", { updatedSubscription });

        await supabaseClient.rpc("update_subscription_tier_limits", {
          user_uuid: user.id,
          new_tier: targetTier,
        });

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            billing_cycle: billingCycle,
            subscription_end_date: new Date(
              updatedSubscription.current_period_end * 1000
            ).toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", user.id);

        return c.json({
          success: true,
          message: "Upgraded successfully",
          new_tier: targetTier,
          billing_cycle: billingCycle,
        });
      } else {
        // Create checkout session for new subscription
        const baseUrl =
          Deno.env.get("NODE_ENV") === "production"
            ? "https://www.hollywoodtable.com"
            : "http://localhost:8080";

        const session = await _stripe.checkout.sessions.create({
          customer: customerId,
          payment_method_types: ["card"],
          line_items: [{ price: priceId, quantity: 1 }],
          mode: "subscription",
          success_url: `${baseUrl}/pricing?session_id={CHECKOUT_SESSION_ID}&success=true`,
          cancel_url: `${baseUrl}/pricing?canceled=true`,
          metadata: {
            supabase_user_id: user.id,
            subscription_tier: targetTier,
            billing_cycle: billingCycle,
          },
          allow_promotion_codes: true,
          billing_address_collection: "auto",
          customer_update: { address: "auto", name: "auto" },
        });

        return c.json({
          success: true,
          checkout_url: session.url,
          session_id: session.id,
          new_tier: targetTier,
          billing_cycle: billingCycle,
        });
      }
    } else {
      // DOWNGRADE LOGIC
      logStep("api", "Processing downgrade", {
        userId: user.id,
        from: currentTier,
        to: targetTier,
      });

      if (targetTier === "Sample the Table") {
        // Downgrade to free tier
        if (subscription.stripe_subscription_id) {
          await _stripe.subscriptions.update(
            subscription.stripe_subscription_id,
            {
              cancel_at_period_end: true,
            }
          );
        }

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            subscription_status: "canceled",
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", user.id);

        return c.json({
          success: true,
          message: "Subscription will be canceled at period end",
          new_tier: targetTier,
          effective_date: subscription.subscription_end_date,
        });
      } else {
        // Downgrade between paid tiers
        if (!subscription.stripe_subscription_id) {
          // No active Stripe subscription, just update database
          await supabaseClient
            .from("user_subscriptions")
            .update({
              subscription_tier: targetTier,
              subscription_status: "active",
              updated_at: new Date().toISOString(),
            })
            .eq("user_id", user.id);

          return c.json({
            success: true,
            message: `Downgraded to ${targetTier}`,
            new_tier: targetTier,
          });
        }

        const newPriceId = getStripePriceId(targetTier, billingCycle);
        if (!newPriceId) {
          return c.json({ error: "Price ID missing for downgrade" }, 500);
        }

        const stripeSubscription = await _stripe.subscriptions.retrieve(
          subscription.stripe_subscription_id
        );
        const updatedSubscription = await _stripe.subscriptions.update(
          subscription.stripe_subscription_id,
          {
            items: [
              { id: stripeSubscription.items.data[0].id, price: newPriceId },
            ],
            proration_behavior: "create_prorations",
          }
        );

        await supabaseClient.rpc("update_subscription_tier_limits", {
          user_uuid: user.id,
          new_tier: targetTier,
        });

        await supabaseClient
          .from("user_subscriptions")
          .update({
            subscription_tier: targetTier,
            billing_cycle: billingCycle,
            subscription_end_date: new Date(
              updatedSubscription.current_period_end * 1000
            ).toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq("user_id", user.id);

        return c.json({
          success: true,
          message: "Downgraded successfully",
          new_tier: targetTier,
          billing_cycle: billingCycle,
          proration_credit:
            "A credit has been applied to your account for the difference",
        });
      }
    }
  } catch (error) {
    logStep("api", "Error in manage-subscription", {
      error: (error as Error).message,
    });
    return c.json(
      {
        error: "Internal server error",
        details: (error as Error).message,
      },
      500
    );
  }
});

// ================================
// WAITLIST ROUTES (/waitlist/*)
// ================================

app.post("/waitlist/send-acknowledgment", async (c) => {
  try {
    const { email } = await c.req.json();

    if (!email) {
      return c.json({ error: "Email is required" }, 400);
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return c.json({ error: "Invalid email format" }, 400);
    }

    const supabaseClient = createSupabaseClient();

    // Use Supabase Auth Admin API generateLink with recovery type as requested
    // This will send an email using the recovery template configured in Supabase
    // The redirect URL will be customized to indicate it's for waitlist acknowledgment
    const { data, error: linkError } =
      await supabaseClient.auth.admin.generateLink({
        type: "recovery",
        email: email,
        options: {
          redirectTo: `${
            Deno.env.get("SITE_URL") || "https://hollywoodtable.com"
          }/waitlist-confirmation`,
          data: {
            type: "waitlist_acknowledgment",
            message:
              "Thank you for joining our beta waitlist! We acknowledge your interest and will notify you when you are picked for the beta program.",
          },
        },
      });

    if (linkError) {
      logStep(
        "api",
        "Failed to generate recovery link for waitlist acknowledgment",
        {
          error: linkError.message,
          email: email,
        }
      );
      return c.json(
        {
          error: "Failed to send acknowledgment email",
          details: linkError.message,
        },
        500
      );
    }

    logStep(
      "api",
      "Waitlist acknowledgment email sent successfully using recovery template",
      {
        email: email,
        actionLinkSent: !!data.properties?.action_link,
        redirectTo: data.properties?.redirect_to,
      }
    );

    return c.json({
      success: true,
      message:
        "Waitlist acknowledgment email sent successfully using recovery template",
      emailSent: true,
    });
  } catch (error) {
    logStep("api", "Internal server error in waitlist acknowledgment", {
      error: (error as Error).message,
    });
    return c.json(
      {
        error: "Internal server error",
        details: (error as Error).message,
      },
      500
    );
  }
});

// ================================
// ADMIN ROUTES (/admin/*)
// ================================

app.post("/admin/invite-users", async (c) => {
  try {
    const { emails, additionalParams } = await c.req.json();

    if (!emails || !Array.isArray(emails)) {
      return c.json({ error: "emails array is required" }, 400);
    }

    const supabaseClient = createSupabaseClient();

    const responses = await Promise.all(
      emails.map(async (email: string) => {
        try {
          let { data: _user } = await supabaseClient
            .from("waitlist")
            .select("*")
            .eq("email", email)
            .single();

          if (!_user) {
            const { error: insertError } = await supabaseClient
              .from("waitlist")
              .insert([{ email, name: "", reason: "" }])
              .select();

            if (insertError) {
              return { email, error: insertError.message };
            }
          }

          const inviteCodes = Array.from({ length: 3 }, () => nanoid());
          const inviteData = { inviteCodes, hasPassword: false };

          const { error: inviteError } =
            await supabaseClient.auth.admin.inviteUserByEmail(email, {
              data: inviteData,
            });

          if (inviteError) {
            return { email, error: inviteError.message };
          }

          const { data: authData, error: userError } =
            await supabaseClient.auth.admin.listUsers();

          if (userError) {
            return { email, error: userError.message };
          }

          const user = authData.users.find((u) => u.email === email);

          if (!user) {
            return { email, error: "User not found after invitation" };
          }

          const inviteCodeInserts = inviteCodes.map((code) => ({
            code,
            expires_at: new Date(
              Date.now() + 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
            used: false,
            from_user: user.id,
          }));

          const { error: insertError } = await supabaseClient
            .from("invite_codes")
            .insert(inviteCodeInserts);

          if (insertError) {
            return { email, error: insertError.message };
          }

          return { email, inviteCodes, userId: user.id };
        } catch (error) {
          return { email, error: (error as Error).message };
        }
      })
    );

    return c.json(responses);
  } catch (error) {
    logStep("api", "Internal server error in admin invite", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// EMAIL TEMPLATES
// ================================

interface EmailTemplateResult {
  subject: string;
  htmlBody: string;
  textBody: string;
}

/**
 * Generates email templates based on template type and data
 *
 * @param templateType Type of email template to generate
 * @param data Data to populate the template with
 * @returns Email template with subject, HTML body, and text body
 */
function getEmailTemplate(
  templateType:
    | "welcome"
    | "password_reset"
    | "invite"
    | "notification"
    | "custom",
  data: Record<string, any>
): EmailTemplateResult {
  let subject = "";
  let content = "";
  let buttonText = "";
  let buttonUrl = "";
  let preheader = "";
  let footerText = "© Hollywood Table. All rights reserved.";

  switch (templateType) {
    case "welcome":
      subject = "Welcome to Hollywood Table";
      preheader = "Your journey into the world of noir begins now.";
      content = `
        <p>Hello ${data.name || "there"},</p>
        <p>Welcome to Hollywood Table! We're thrilled to have you join our community of noir enthusiasts.</p>
        <p>Get ready to step into a world of intrigue, mystery, and captivating characters.</p>
        <p>Your account is now active and ready to use.</p>
      `;
      buttonText = "Start Exploring";
      buttonUrl = data.loginUrl || "https://www.hollywoodtable.com/login";
      break;

    case "password_reset":
      subject = "Reset Your Hollywood Table Password";
      preheader = "Follow these instructions to reset your password.";
      content = `
        <p>Hello ${data.name || "there"},</p>
        <p>We received a request to reset your password for your Hollywood Table account.</p>
        <p>Click the button below to reset your password. If you didn't request this, you can safely ignore this email.</p>
      `;
      buttonText = "Reset Password";
      buttonUrl =
        data.resetUrl || "https://www.hollywoodtable.com/reset-password";
      break;

    case "invite":
      subject = "You're Invited to Hollywood Table";
      preheader = "Join the exclusive world of noir storytelling.";
      content = `
        <p>Hello,</p>
        <p>${
          data.inviterName || "Someone"
        } has invited you to join Hollywood Table, an immersive noir experience.</p>
        <p>Use the button below to accept your invitation and create your account.</p>
        ${
          data.inviteCode
            ? `<p>Your invite code: <strong>${data.inviteCode}</strong></p>`
            : ""
        }
      `;
      buttonText = "Accept Invitation";
      buttonUrl = data.inviteUrl || "https://www.hollywoodtable.com/signup";
      break;

    case "notification":
      subject = data.subject || "Notification from Hollywood Table";
      preheader = data.preheader || "You have a new notification.";
      content = `
        <p>Hello ${data.name || "there"},</p>
        <p>${
          data.message || "You have a new notification from Hollywood Table."
        }</p>
      `;
      if (data.actionUrl) {
        buttonText = data.actionText || "View Details";
        buttonUrl = data.actionUrl;
      }
      break;

    case "custom":
      subject = data.subject || "Message from Hollywood Table";
      preheader = data.preheader || "";
      content = data.content || "";
      if (data.buttonText && data.buttonUrl) {
        buttonText = data.buttonText;
        buttonUrl = data.buttonUrl;
      }
      if (data.footerText) {
        footerText = data.footerText;
      }
      break;

    default:
      throw new Error(`Unknown template type: ${templateType}`);
  }

  // Generate HTML using the Postmark template generator
  const htmlBody = generateEmailHtml({
    type: templateType,
    title: subject,
    preheader,
    content,
    buttonText,
    buttonUrl,
    footerText,
  });

  // Generate plain text version (simplified)
  let textBody = `${subject}\n\n`;

  // Strip HTML tags for text version
  const stripHtml = (html: string) => {
    return html
      .replace(/<[^>]*>/g, "")
      .replace(/\s+/g, " ")
      .trim();
  };

  textBody += stripHtml(content);

  if (buttonText && buttonUrl) {
    textBody += `\n\n${buttonText}: ${buttonUrl}`;
  }

  textBody += `\n\n${footerText}`;

  return {
    subject,
    htmlBody,
    textBody,
  };
}

// ================================
// EMAIL ROUTES (/email/*)
// ================================

app.post("/email/send", async (c) => {
  try {
    logStep("api", "Starting email send request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(
        {
          error: "User not authenticated.",
          details: authError,
        },
        401
      );
    }

    const {
      template_type,
      to_email,
      template_data,
      from_email = "<EMAIL>",
      reply_to,
      cc,
      bcc,
      tag,
    } = await c.req.json();

    if (!template_type || !to_email || !template_data) {
      return c.json(
        {
          error:
            "Missing required fields: template_type, to_email, and template_data are required.",
        },
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to_email)) {
      return c.json({ error: "Invalid recipient email format" }, 400);
    }

    // Get email template
    let emailTemplate;
    try {
      emailTemplate = getEmailTemplate(
        template_type as any,
        template_data as any
      );
    } catch (templateError) {
      return c.json(
        {
          error: "Invalid template type or data",
          details: (templateError as Error).message,
        },
        400
      );
    }

    // Send email using Postmark
    try {
      const result = await sendEmail({
        from: from_email,
        to: to_email,
        subject: emailTemplate.subject,
        htmlBody: emailTemplate.htmlBody,
        textBody: emailTemplate.textBody,
        replyTo: reply_to,
        cc,
        bcc,
        tag,
      });

      logStep("api", "Email sent successfully", {
        to: to_email,
        template: template_type,
        messageId: result.MessageID,
      });

      return c.json({
        success: true,
        message: "Email sent successfully",
        messageId: result.MessageID,
      });
    } catch (emailError) {
      logStep("api", "Failed to send email", {
        error: (emailError as Error).message,
        to: to_email,
        template: template_type,
      });

      return c.json(
        {
          error: "Failed to send email",
          details: (emailError as Error).message,
        },
        500
      );
    }
  } catch (error) {
    logStep("api", "Internal server error in email send", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// Custom email route that allows sending emails with custom content
app.post("/email/send-custom", async (c) => {
  try {
    logStep("api", "Starting custom email send request");

    const { user, error: authError } = await getAuthenticatedUser(c);
    if (authError || !user) {
      return c.json(
        {
          error: "User not authenticated.",
          details: authError,
        },
        401
      );
    }

    // Check if user has admin role
    const supabaseClient = createSupabaseClientFromContext(c);
    const { data: profile, error: profileError } = await supabaseClient
      .from("profiles")
      .select("role")
      .eq("id", user.id)
      .single();

    if (profileError) {
      return c.json(
        {
          error: "Failed to verify user permissions",
          details: profileError.message,
        },
        500
      );
    }

    // Only allow admins to send custom emails
    if (profile?.role !== "admin") {
      return c.json({ error: "Unauthorized. Admin role required." }, 403);
    }

    const {
      to_email,
      subject,
      html_content,
      text_content,
      from_email = "<EMAIL>",
      reply_to,
      cc,
      bcc,
      tag,
    } = await c.req.json();

    if (!to_email || !subject || (!html_content && !text_content)) {
      return c.json(
        {
          error:
            "Missing required fields: to_email, subject, and either html_content or text_content are required.",
        },
        400
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(to_email)) {
      return c.json({ error: "Invalid recipient email format" }, 400);
    }

    // Send email using Postmark
    try {
      const result = await sendEmail({
        from: from_email,
        to: to_email,
        subject: subject,
        htmlBody: html_content,
        textBody: text_content,
        replyTo: reply_to,
        cc,
        bcc,
        tag,
      });

      logStep("api", "Custom email sent successfully", {
        to: to_email,
        subject: subject,
        messageId: result.MessageID,
      });

      return c.json({
        success: true,
        message: "Custom email sent successfully",
        messageId: result.MessageID,
      });
    } catch (emailError) {
      logStep("api", "Failed to send custom email", {
        error: (emailError as Error).message,
        to: to_email,
      });

      return c.json(
        {
          error: "Failed to send custom email",
          details: (emailError as Error).message,
        },
        500
      );
    }
  } catch (error) {
    logStep("api", "Internal server error in custom email send", {
      error: (error as Error).message,
    });
    return c.json({ error: "Internal server error" }, 500);
  }
});

// ================================
// HEALTH CHECK
// ================================

app.get("/health", (c) => {
  return c.json({
    status: "ok",
    service: "consolidated-api",
    features: {
      auto_subscription_creation:
        "All routes automatically ensure users have a free subscription record if none exists (ensureUserSubscription function uses service role for all operations)",
      message_tracking_only:
        "System now tracks message usage only (conversations are no longer counted toward limits)",
    },
    routes: {
      chat: ["/chat"],
      usage: ["/usage/check-limits (action_type: 'message' only)"],
      billing: [
        "/billing/subscription",
        "/billing/customer-portal",
        "/billing/purchase-credits",
        "/billing/manage-subscription (supports action: 'cancel_keep_credits')",
      ],
      email: ["/email/send", "/email/send-custom"],
      waitlist: ["/waitlist/send-acknowledgment"],
      admin: ["/admin/invite-users"],
      webhooks: ["/webhooks/stripe-subscription", "/webhooks/stripe-payg"],
    },
  });
});

app.get("/", (c) => {
  return c.text("Welcome to the Hollywood Table Consolidated API");
});

// Serve the app
Deno.serve(app.fetch);
