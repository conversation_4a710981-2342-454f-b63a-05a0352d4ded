# Configuration for npm package dependencies
# For more information on using npm packages with Edge Functions, see:
# https://supabase.com/docs/guides/functions/import-maps#importing-from-npm

# Enable automatic installation of peer dependencies
auto-install-peers=true

# Set registry for npm packages
registry=https://registry.npmjs.org/

# Disable fund and audit notifications for cleaner output
fund=false
audit=false

# Use exact versions to avoid compatibility issues
save-exact=true 