import { createClient } from "jsr:@supabase/supabase-js@2";
import type { Context } from "jsr:@hono/hono@^4.8.2";
import { logStep } from "./logger.ts";

export const createSupabaseClient = (authToken?: string) => {
  return createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
    authToken
      ? {
          global: {
            headers: {
              Authorization: authToken,
            },
          },
        }
      : { auth: { persistSession: false } }
  );
};

export const createSupabaseClientFromContext = (c: Context) => {
  const authHeader = c.req.header("authorization");
  return createSupabaseClient(authHeader);
};

export const getAuthenticatedUser = async (c: Context) => {
  const authHeader = c.req.header("authorization");
  if (!authHeader) {
    return { user: null, error: "No authorization header provided" };
  }

  const supabaseClient = createSupabaseClient(authHeader);
  const token = authHeader.replace("Bearer ", "");
  
  try {
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    
    if (userError || !userData.user) {
      return { user: null, error: "User not authenticated" };
    }

    return { user: userData.user, error: null };
  } catch (error) {
    return { user: null, error: "Authentication failed" };
  }
};

// Helper function to ensure user has a subscription record
export const ensureUserSubscription = async (userId: string) => {
  logStep("supabase", "Checking for user subscription record", { userId });
  
  // Use service role client for all operations to bypass RLS
  const serviceRoleClient = createSupabaseClient();
  
  // Check if subscription exists
  const { data: subscription, error: subError } = await serviceRoleClient
    .from("user_subscriptions")
    .select("id, subscription_tier")
    .eq("user_id", userId)
    .single();

  if (subError?.code === 'PGRST116' || !subscription) {
    // No subscription found, create default free subscription
    logStep("supabase", "Creating default free subscription for user", { userId });
    
    const { data: newSubscription, error: createError } = await serviceRoleClient
      .from("user_subscriptions")
      .insert({
        user_id: userId,
        subscription_tier: 'Sample the Table',
        subscription_status: 'active',
        billing_cycle: 'monthly',
        monthly_message_limit: 40,
        messages_used_this_period: 0,
        payg_credits_remaining: 0,
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      })
      .select("id, subscription_tier")
      .single();

    if (createError) {
      logStep("supabase", "Failed to create user subscription", { error: createError, userId });
      throw new Error(`Failed to create user subscription: ${createError.message}`);
    }

    logStep("supabase", "Successfully created user subscription", { 
      userId, 
      subscriptionId: newSubscription.id,
      tier: newSubscription.subscription_tier 
    });
    
    return newSubscription;
  } else if (subError) {
    logStep("supabase", "Database error checking subscription", { error: subError, userId });
    throw new Error(`Database error: ${subError.message}`);
  }

  logStep("supabase", "User subscription record exists", { 
    userId, 
    subscriptionId: subscription.id,
    tier: subscription.subscription_tier 
  });
  
  return subscription;
}; 