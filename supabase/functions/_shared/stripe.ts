import Stripe from "npm:stripe@^17.7.0";
import { logStep } from "./logger.ts";

// Export Stripe type for use in other modules
export { Stripe };

export const createStripeClient = () => {
  const isProduction = Deno.env.get("NODE_ENV") === "production";
  const stripeKey = isProduction 
    ? Deno.env.get("STRIPE_LIVE_SECRET_KEY")
    : Deno.env.get("STRIPE_TEST_SECRET_KEY");
  // Log the stripe key using logStep
  logStep("stripe", "Creating Stripe client", { stripeKey });
  return new Stripe(stripeKey ?? "", {
    apiVersion: "2025-05-28.basil",
  });
}; 