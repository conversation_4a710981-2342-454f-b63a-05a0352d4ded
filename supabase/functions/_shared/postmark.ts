/**
 * Postmark email service
 * Uses the Postmark API directly without dependencies
 */

interface PostmarkEmailOptions {
  from: string;
  to: string;
  subject: string;
  htmlBody?: string;
  textBody?: string;
  replyTo?: string;
  cc?: string;
  bcc?: string;
  tag?: string;
  metadata?: Record<string, string>;
  attachments?: Array<{
    name: string;
    content: string; // Base64 encoded content
    contentType: string;
  }>;
}

export interface EmailTemplate {
  type: 'welcome' | 'password_reset' | 'invite' | 'notification' | 'custom';
  title: string;
  preheader?: string;
  content: string;
  buttonText?: string;
  buttonUrl?: string;
  footerText?: string;
}

const serverToken = Deno.env.get("POSTMARK_SERVER_TOKEN") || "************************************";
const defaultFromEmail = Deno.env.get("DEFAULT_FROM_EMAIL") || "<EMAIL>";

/**
 * Generates a standardized HTML email body using a template
 * 
 * @param template Email template configuration
 * @returns HTML string for the email body
 */
export function generateEmailHtml(template: EmailTemplate): string {
  const {
    type,
    title,
    preheader = '',
    content,
    buttonText,
    buttonUrl,
    footerText = '© Hollywood Table. All rights reserved.'
  } = template;

  // Button HTML only if both text and URL are provided
  const buttonHtml = buttonText && buttonUrl
    ? `<table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 100%;">
        <tr>
          <td align="center">
            <table border="0" cellpadding="0" cellspacing="0">
              <tr>
                <td align="center" bgcolor="#1a1a1a" style="border-radius: 4px;">
                  <a href="${buttonUrl}" target="_blank" style="display: inline-block; padding: 16px 36px; font-family: 'Helvetica', Arial, sans-serif; font-size: 16px; color: #ffffff; text-decoration: none; border-radius: 4px;">
                    ${buttonText}
                  </a>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>`
    : '';

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
      <title>${title}</title>
      <style>
        @media only screen and (max-width: 620px) {
          table.body h1 {
            font-size: 28px !important;
            margin-bottom: 10px !important;
          }
          
          table.body p,
          table.body ul,
          table.body ol,
          table.body td,
          table.body span,
          table.body a {
            font-size: 16px !important;
          }
          
          table.body .wrapper,
          table.body .article {
            padding: 10px !important;
          }
          
          table.body .content {
            padding: 0 !important;
          }
          
          table.body .container {
            padding: 0 !important;
            width: 100% !important;
          }
          
          table.body .main {
            border-left-width: 0 !important;
            border-radius: 0 !important;
            border-right-width: 0 !important;
          }
        }
      </style>
    </head>
    <body style="background-color: #f6f6f6; font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; line-height: 1.4; margin: 0; padding: 0; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
      <span style="display: none !important; visibility: hidden; mso-hide: all; font-size: 1px; color: #fff; line-height: 1px; max-height: 0px; max-width: 0px; opacity: 0; overflow: hidden;">${preheader}</span>
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f6f6f6; width: 100%;" width="100%" bgcolor="#f6f6f6">
        <tr>
          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
          <td class="container" style="font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 580px; padding: 10px; width: 580px; margin: 0 auto;" width="580" valign="top">
            <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 580px; padding: 10px;">
              <!-- START CENTERED WHITE CONTAINER -->
              <table role="presentation" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border-radius: 3px; width: 100%;" width="100%">
                <!-- START MAIN CONTENT AREA -->
                <tr>
                  <td class="wrapper" style="font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;" valign="top">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                      <tr>
                        <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">
                          <div style="text-align: center; margin-bottom: 24px;">
                            <img src="https://www.hollywoodtable.com/logo.png" alt="Hollywood Table" width="180" style="max-width: 180px;">
                          </div>
                          <h1 style="color: #000000; font-family: sans-serif; font-weight: 400; line-height: 1.4; margin: 0; margin-bottom: 30px; font-size: 35px; text-align: center;">${title}</h1>
                          <div style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 30px;">
                            ${content}
                          </div>
                          ${buttonHtml}
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <!-- END MAIN CONTENT AREA -->
              </table>
              <!-- START FOOTER -->
              <div class="footer" style="clear: both; margin-top: 10px; text-align: center; width: 100%;">
                <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                  <tr>
                    <td class="content-block" style="font-family: sans-serif; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: #999999; font-size: 12px; text-align: center;" valign="top" align="center">
                      <span class="apple-link" style="color: #999999; font-size: 12px; text-align: center;">${footerText}</span>
                    </td>
                  </tr>
                </table>
              </div>
              <!-- END FOOTER -->
            </div>
          </td>
          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
        </tr>
      </table>
    </body>
    </html>
  `;
}

/**
 * Sends a single email via Postmark API
 * 
 * Note: This function should be used from a server environment to avoid CORS issues.
 * For client-side usage, you should create a server endpoint that uses this function.
 * 
 * @param options Email options
 * @returns Response from Postmark API
 */
export async function sendEmail(options: PostmarkEmailOptions) {
  if (!options.htmlBody && !options.textBody) {
    throw new Error('Either htmlBody or textBody must be provided');
  }
  
  try {
    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': serverToken
      },
      body: JSON.stringify({
        From: options.from,
        To: options.to,
        Subject: options.subject,
        HtmlBody: options.htmlBody,
        TextBody: options.textBody,
        ReplyTo: options.replyTo,
        Cc: options.cc,
        Bcc: options.bcc,
        Tag: options.tag,
        Metadata: options.metadata,
        Attachments: options.attachments
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Postmark API error: ${errorData.Message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending email via Postmark:', error);
    throw error;
  }
}
