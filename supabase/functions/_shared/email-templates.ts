/**
 * Email template generation system
 * Provides standardized email templates for various use cases
 */

import { generateEmailHtml } from "./postmark.ts";

export interface EmailTemplateResult {
  subject: string;
  htmlBody: string;
  textBody: string;
}

/**
 * Generates email templates based on template type and data
 * 
 * @param templateType Type of email template to generate
 * @param data Data to populate the template with
 * @returns Email template with subject, HTML body, and text body
 */
export function getEmailTemplate(
  templateType: 'welcome' | 'password_reset' | 'invite' | 'notification' | 'custom' | 'subscription_confirmation' | 'subscription_cancelled',
  data: Record<string, any>
): EmailTemplateResult {
  let subject = '';
  let content = '';
  let buttonText = '';
  let buttonUrl = '';
  let preheader = '';
  let footerText = '© Hollywood Table. All rights reserved.';
  
  const baseUrl = Deno.env.get("NODE_ENV") === "production"
    ? "https://www.hollywoodtable.com"
    : "http://localhost:8080";
  
  switch (templateType) {
    case 'welcome':
      subject = 'Welcome to Hollywood Table';
      preheader = 'Your journey into the world of noir begins now.';
      content = `
        <p>Hello ${data.userName || 'there'},</p>
        <p>Welcome to Hollywood Table! We're thrilled to have you join our community of noir enthusiasts.</p>
        <p>Get ready to step into a world of intrigue, mystery, and captivating characters.</p>
        <p>Your account is now active and ready to use.</p>
      `;
      buttonText = 'Start Exploring';
      buttonUrl = data.loginUrl || `${baseUrl}/login`;
      break;
      
    case 'password_reset':
      subject = 'Reset Your Hollywood Table Password';
      preheader = 'Follow these instructions to reset your password.';
      content = `
        <p>Hello ${data.userName || 'there'},</p>
        <p>We received a request to reset your password for your Hollywood Table account.</p>
        <p>Click the button below to reset your password. If you didn't request this, you can safely ignore this email.</p>
      `;
      buttonText = 'Reset Password';
      buttonUrl = data.resetUrl || `${baseUrl}/reset-password`;
      break;
      
    case 'invite':
      subject = 'You\'re Invited to Hollywood Table';
      preheader = 'Join the exclusive world of noir storytelling.';
      content = `
        <p>Hello,</p>
        <p>${data.inviterName || 'Someone'} has invited you to join Hollywood Table, an immersive noir experience.</p>
        <p>Use the button below to accept your invitation and create your account.</p>
        ${data.inviteCode ? `<p>Your invite code: <strong>${data.inviteCode}</strong></p>` : ''}
      `;
      buttonText = 'Accept Invitation';
      buttonUrl = data.signupUrl || `${baseUrl}/signup`;
      break;
      
    case 'subscription_confirmation':
      subject = 'Subscription Confirmed - Hollywood Table';
      preheader = 'Your subscription has been activated.';
      content = `
        <p>Hello ${data.userName || 'there'},</p>
        <p>Thank you for subscribing to Hollywood Table! Your <strong>${data.subscriptionTier || 'premium'}</strong> subscription is now active.</p>
        ${data.billingCycle ? `<p><strong>Billing Cycle:</strong> ${data.billingCycle}</p>` : ''}
        ${data.nextBillingDate ? `<p><strong>Next Billing Date:</strong> ${new Date(data.nextBillingDate).toLocaleDateString()}</p>` : ''}
        <p>You now have access to all the features included in your subscription tier. Start exploring and chatting with our characters!</p>
      `;
      buttonText = 'Start Chatting';
      buttonUrl = `${baseUrl}/chat`;
      break;
      
    case 'subscription_cancelled':
      subject = 'Subscription Cancelled - Hollywood Table';
      preheader = 'Your subscription has been cancelled.';
      content = `
        <p>Hello ${data.userName || 'there'},</p>
        <p>We're sorry to see you go! Your <strong>${data.subscriptionTier || 'premium'}</strong> subscription has been cancelled.</p>
        <p>You'll continue to have access to your subscription features until your current billing period ends.</p>
        ${data.nextBillingDate ? `<p><strong>Access Until:</strong> ${new Date(data.nextBillingDate).toLocaleDateString()}</p>` : ''}
        <p>If you change your mind, you can always resubscribe from your account settings.</p>
      `;
      buttonText = 'View Plans';
      buttonUrl = `${baseUrl}/pricing`;
      break;
      
    case 'notification':
      subject = data.subject || 'Notification from Hollywood Table';
      preheader = data.preheader || 'You have a new notification.';
      content = `
        <p>Hello ${data.userName || 'there'},</p>
        <p>${data.message || 'You have a new notification from Hollywood Table.'}</p>
      `;
      if (data.actionUrl) {
        buttonText = data.actionText || 'View Details';
        buttonUrl = data.actionUrl;
      }
      break;
      
    case 'custom':
      subject = data.subject || 'Message from Hollywood Table';
      preheader = data.preheader || '';
      content = data.content || '';
      if (data.buttonText && data.buttonUrl) {
        buttonText = data.buttonText;
        buttonUrl = data.buttonUrl;
      }
      if (data.footerText) {
        footerText = data.footerText;
      }
      break;
      
    default:
      throw new Error(`Unknown template type: ${templateType}`);
  }
  
  // Generate HTML using the Postmark template generator
  const htmlBody = generateEmailHtml({
    type: templateType,
    title: subject,
    preheader,
    content,
    buttonText,
    buttonUrl,
    footerText
  });
  
  // Generate plain text version (simplified)
  let textBody = `${subject}\n\n`;
  
  // Strip HTML tags for text version
  const stripHtml = (html: string) => {
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  };
  
  textBody += stripHtml(content);
  
  if (buttonText && buttonUrl) {
    textBody += `\n\n${buttonText}: ${buttonUrl}`;
  }
  
  textBody += `\n\n${footerText}`;
  
  return {
    subject,
    htmlBody,
    textBody
  };
}