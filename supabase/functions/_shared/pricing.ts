// Type definitions for better type safety
type BillingCycle = 'monthly' | 'annual';
type DisplayTier = 'Reserved Seat' | 'VIP Table' | 'Sample the Table';
type InternalTier = 'reserved_seat' | 'vip_table' | 'sample_the_table';
type Environment = 'sandbox' | 'live';

// Environment configuration - automatically determines based on NODE_ENV
// @ts-ignore - Deno global is available in Supabase Edge Functions
const CURRENT_ENVIRONMENT: Environment = (Deno.env.get('NODE_ENV') === 'production') ? 'live' : 'sandbox';

// Sandbox/Test Stripe Price IDs
const SANDBOX_TIER_CONFIG = {
  'Reserved Seat': {
    internal: 'reserved_seat' as const,
    prices: {
      monthly: 'price_1RcVP6BAfMyOv5wFnlr1nHTX',
      annual: 'price_1RcVQsBAfMyOv5wFQL1QnKX0'
    }
  },
  'VIP Table': {
    internal: 'vip_table' as const,
    prices: {
      monthly: 'price_1RcVSbBAfMyOv5wFPser98dF',
      annual: 'price_1RcVUUBAfMyOv5wFa23l3xnS'
    }
  },
  'Sample the Table': {
    internal: 'sample_the_table' as const,
    prices: {}
  }
} as const;

// Live Production Stripe Price IDs - REPLACE WITH YOUR LIVE PRICE IDs
const LIVE_TIER_CONFIG = {
  'Reserved Seat': {
    internal: 'reserved_seat' as const,
    prices: {
      monthly: 'price_0Rdu7HZTM9hZsnBumQrnp9gb',  // Replace with your live price ID
      annual: 'price_0Rdu8MZTM9hZsnBurDHQi2oL'     // Replace with your live price ID
    }
  },
  'VIP Table': {
    internal: 'vip_table' as const,
    prices: {
      monthly: 'price_0Rdu95ZTM9hZsnBuHDGMVsRZ',       // Replace with your live price ID
      annual: 'price_0Rdu9SZTM9hZsnBuARgKMWJi'          // Replace with your live price ID
    }
  },
  'Sample the Table': {
    internal: 'sample_the_table' as const,
    prices: {}
  }
} as const;

// Get the current tier configuration based on environment
const TIER_CONFIG = (CURRENT_ENVIRONMENT as string) === 'live' ? LIVE_TIER_CONFIG : SANDBOX_TIER_CONFIG;

// Sandbox PAYG Credit Packages
const SANDBOX_PAYG_PRICES = {
  starter: 'price_1RcVNBBAfMyOv5wFHxNi3zoF',    // 25 credits for $4.99
  writers: 'price_1RdmX6BAfMyOv5wFmiosSwkf',    // 75 credits for $9.99
  pro: 'price_1RdmXxBAfMyOv5wFvkQQeP4x'         // 200 credits for $19.99
} as const;

// Live PAYG Credit Packages - REPLACE WITH YOUR LIVE PRICE IDs
const LIVE_PAYG_PRICES = {
  starter: 'price_0Rdu5YZTM9hZsnBu8dhMyYjQ',    // Replace with your live price ID for 25 credits
  writers: 'price_0Rdu5vZTM9hZsnBuTUibGy3T',    // Replace with your live price ID for 75 credits
  pro: 'price_0Rdu67ZTM9hZsnBuzMFb21ea'             // Replace with your live price ID for 200 credits
} as const;

// Get the current PAYG prices based on environment
const PAYG_PRICES = (CURRENT_ENVIRONMENT as string) === 'live' ? LIVE_PAYG_PRICES : SANDBOX_PAYG_PRICES;

// PAYG Package Details
export const PAYG_PACKAGES = {
  starter: {
    credits: 25,
    price_cents: 499,
    name: "Starter",
    description: "25 additional message credits"
  },
  writers: {
    credits: 75,
    price_cents: 999,
    name: "Writer's",
    description: "75 additional message credits",
    popular: true,
    savings: "Best Value"
  },
  pro: {
    credits: 200,
    price_cents: 1999,
    name: "Pro",
    description: "200 additional message credits",
    savings: "Most Credits"
  }
} as const;

export type PaygPackageType = keyof typeof PAYG_PACKAGES;

export interface PaygPackage {
  credits: number;
  price_cents: number;
  name: string;
  description: string;
  popular?: boolean;
  savings?: string;
}

export interface PaygPackageWithId extends PaygPackage {
  id: string;
  price: number; // price in dollars (converted from price_cents)
}

// Create tier mapping from the configuration
const tierMap: Record<string, string> = Object.fromEntries(
  Object.entries(TIER_CONFIG).map(([display, config]) => [display, config.internal])
);

/**
 * Converts a display tier name to its internal representation
 * @param displayTier - The display name of the tier (e.g., "Reserved Seat")
 * @returns The internal tier name or null if not found
 */
export const getInternalTierName = (displayTier: string): string | null => {
  return tierMap[displayTier] ?? null;
};

/**
 * Converts an internal tier name to its display representation
 * @param internalTier - The internal name of the tier (e.g., "reserved_seat")
 * @returns The display tier name or null if not found
 */
export const getDisplayTierName = (internalTier: string): string | null => {
  const entry = Object.entries(tierMap).find(([, internal]) => internal === internalTier);
  return entry?.[0] ?? null;
};

/**
 * Gets the Stripe price ID for a given tier and billing cycle
 * @param tier - The tier name (display or internal format)
 * @param billingCycle - The billing cycle ('monthly' or 'annual'), defaults to 'monthly'
 * @returns The Stripe price ID or null if not found
 */
export const getStripePriceId = (tier: string, billingCycle?: BillingCycle): string | null => {
  // Try to find tier in configuration (supports both display and internal names)
  const tierConfig = Object.values(TIER_CONFIG).find(
    config => config.internal === tier
  ) ?? Object.entries(TIER_CONFIG).find(
    ([displayName]) => displayName === tier
  )?.[1];

  if (!tierConfig) {
    return null;
  }

  const cycle = billingCycle ?? 'monthly';
  return tierConfig.prices[cycle] ?? null;
};

/**
 * Maps a Stripe price ID back to its corresponding subscription tier
 * @param priceId - The Stripe price ID
 * @returns The display tier name or null if not found
 */
export const getSubscriptionTierFromPriceId = (priceId: string): string | null => {
  // Check against all configured subscription tiers
  for (const [displayTier, config] of Object.entries(TIER_CONFIG)) {
    for (const cycle of ['monthly', 'annual'] as const) {
      if (config.prices[cycle] === priceId) {
        return displayTier;
      }
    }
  }
  
  return null;
};

/**
 * Gets the Stripe price ID for a PAYG credit package
 * @param packageType - The package type ('starter', 'writers', 'pro')
 * @returns The Stripe price ID or null if not found
 */
export const getPaygPriceId = (packageType: string): string | null => {
  return PAYG_PRICES[packageType as keyof typeof PAYG_PRICES] ?? null;
};

/**
 * Gets all PAYG price configurations
 * @returns Object containing all PAYG price IDs
 */
export const getPaygPrices = () => PAYG_PRICES;

/**
 * Checks if a price ID corresponds to a PAYG (pay-as-you-go) purchase
 * @param priceId - The Stripe price ID to check
 * @returns True if the price ID is for PAYG credits, false otherwise
 */
export const isPaygPriceId = (priceId: string): boolean => {
  return Object.values(PAYG_PRICES).includes(priceId as any);
};