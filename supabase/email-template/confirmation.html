<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your Hollywood Table Account</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poiret+One:wght@400&family=Cinzel:wght@400;600&family=Source+Sans+Pro:wght@300;400;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Source Sans Pro', Arial, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2c2c2c 100%);
            color: #f5f5dc;
            line-height: 1.6;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(145deg, #1a1a1a 0%, #0f0f0f 100%);
            border: 2px solid #d4af37;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }
        
        /* Art Deco border decoration */
        .email-container::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 6px;
            pointer-events: none;
            z-index: 1;
        }
        
        .header {
            background: linear-gradient(135deg, #8b1538 0%, #6d1027 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(45deg, transparent 40%, rgba(212, 175, 55, 0.1) 42%, rgba(212, 175, 55, 0.1) 44%, transparent 46%),
                linear-gradient(-45deg, transparent 40%, rgba(212, 175, 55, 0.1) 42%, rgba(212, 175, 55, 0.1) 44%, transparent 46%);
            background-size: 40px 40px, 40px 40px;
            opacity: 0.3;
            z-index: 0;
        }
        
        .logo {
            font-family: 'Cinzel', serif;
            font-size: 32px;
            font-weight: 600;
            color: #d4af37;
            text-shadow: 0 2px 10px rgba(212, 175, 55, 0.5);
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .tagline {
            font-family: 'Poiret One', sans-serif;
            font-size: 16px;
            color: #f5f5dc;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px 30px;
            position: relative;
            z-index: 2;
        }
        
        .greeting {
            font-family: 'Cinzel', serif;
            font-size: 24px;
            color: #d4af37;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .message {
            font-size: 16px;
            color: #f5f5dc;
            margin-bottom: 25px;
            text-align: center;
            line-height: 1.7;
        }
        
        .highlight {
            color: #d4af37;
            font-weight: 600;
        }
        
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            color: #0f0f0f;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 6px;
            font-family: 'Source Sans Pro', sans-serif;
            font-weight: 600;
            font-size: 18px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.3) 20%, rgba(212, 175, 55, 0.8) 50%, rgba(212, 175, 55, 0.3) 80%, transparent 100%);
            margin: 30px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            left: 50%;
            top: -6px;
            transform: translateX(-50%) rotate(45deg);
            width: 12px;
            height: 12px;
            background: #d4af37;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }
        
        .welcome-icon {
            text-align: center;
            margin: 30px 0;
        }
        
        .welcome-icon-emoji {
            font-size: 48px;
            display: block;
            margin-bottom: 15px;
            animation: glow-pulse 2s ease-in-out infinite;
        }
        
        @keyframes glow-pulse {
            0%, 100% { 
                filter: drop-shadow(0 0 10px rgba(212, 175, 55, 0.3));
            }
            50% { 
                filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.6));
            }
        }
        
        .security-note {
            background: rgba(212, 175, 55, 0.1);
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 6px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .security-note-title {
            font-family: 'Poiret One', sans-serif;
            font-size: 16px;
            color: #d4af37;
            margin-bottom: 10px;
        }
        
        .security-note-text {
            font-size: 14px;
            color: #f5f5dc;
            opacity: 0.8;
        }
        
        .footer {
            background: #0f0f0f;
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(212, 175, 55, 0.2);
        }
        
        .footer-text {
            font-size: 14px;
            color: #f5f5dc;
            opacity: 0.7;
            margin-bottom: 10px;
        }
        
        .footer-link {
            color: #d4af37;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        /* Mobile responsive */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .logo {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 20px;
            }
            
            .message {
                font-size: 15px;
            }
            
            .cta-button {
                padding: 14px 30px;
                font-size: 16px;
            }
            
            .content, .header, .footer {
                padding: 30px 20px;
            }
            
            .welcome-icon-emoji {
                font-size: 36px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">HOLLYWOOD TABLE</div>
            <div class="tagline">Where Stories Come to Life</div>
        </div>
        
        <div class="content">
            <div class="greeting">Almost There!</div>
            
            <div class="welcome-icon">
                <span class="welcome-icon-emoji">🎬</span>
            </div>
            
            <p class="message">
                Welcome to <span class="highlight">Hollywood Table</span>! 
                You're just one step away from joining our exclusive community of storytellers 
                and screenwriting enthusiasts.
            </p>
            
            <p class="message">
                To complete your registration and secure your seat at the table, 
                please confirm your email address by clicking the button below.
            </p>
            
            <div class="cta-container">
                <a href="{{ .ConfirmationURL }}" class="cta-button">
                    Confirm Your Account
                </a>
            </div>
            
            <div class="divider"></div>
            
            <div class="security-note">
                <div class="security-note-title">🔒 Security Notice</div>
                <div class="security-note-text">
                    This confirmation link is valid for 24 hours and can only be used once. 
                    If you didn't create an account with Hollywood Table, you can safely ignore this email.
                </div>
            </div>
            
            <p class="message" style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                Once confirmed, you'll have access to intimate conversations with legendary screenwriters, 
                exclusive storytelling insights, and our sophisticated noir-inspired community.
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Having trouble? Copy and paste this link into your browser:
            </p>
            <p class="footer-text">
                <a href="{{ .ConfirmationURL }}" class="footer-link">{{ .ConfirmationURL }}</a>
            </p>
            <p class="footer-text" style="margin-top: 20px;">
                Questions? Contact us at <a href="mailto:support@{{ .SiteURL }}" class="footer-link">support@{{ .SiteURL }}</a>
            </p>
        </div>
    </div>
</body>
</html> 