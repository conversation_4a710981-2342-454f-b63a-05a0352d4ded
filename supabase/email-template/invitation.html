<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Invitation to Hollywood Table</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poiret+One:wght@400&family=Cinzel:wght@400;600&family=Source+Sans+Pro:wght@300;400;600&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Source Sans Pro', Arial, sans-serif;
            background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #2c2c2c 100%);
            color: #f5f5dc;
            line-height: 1.6;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(145deg, #1a1a1a 0%, #0f0f0f 100%);
            border: 2px solid #d4af37;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            position: relative;
        }
        
        /* Art Deco border decoration */
        .email-container::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            bottom: 8px;
            border: 1px solid rgba(212, 175, 55, 0.3);
            border-radius: 6px;
            pointer-events: none;
            z-index: 1;
        }
        
        .header {
            background: linear-gradient(135deg, #8b1538 0%, #6d1027 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(45deg, transparent 40%, rgba(212, 175, 55, 0.1) 42%, rgba(212, 175, 55, 0.1) 44%, transparent 46%),
                linear-gradient(-45deg, transparent 40%, rgba(212, 175, 55, 0.1) 42%, rgba(212, 175, 55, 0.1) 44%, transparent 46%);
            background-size: 40px 40px, 40px 40px;
            opacity: 0.3;
            z-index: 0;
        }
        
        .logo {
            font-family: 'Cinzel', serif;
            font-size: 32px;
            font-weight: 600;
            color: #d4af37;
            text-shadow: 0 2px 10px rgba(212, 175, 55, 0.5);
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .tagline {
            font-family: 'Poiret One', sans-serif;
            font-size: 16px;
            color: #f5f5dc;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px 30px;
            position: relative;
            z-index: 2;
        }
        
        .greeting {
            font-family: 'Cinzel', serif;
            font-size: 24px;
            color: #d4af37;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 0 10px rgba(212, 175, 55, 0.3);
        }
        
        .message {
            font-size: 16px;
            color: #f5f5dc;
            margin-bottom: 25px;
            text-align: center;
            line-height: 1.7;
        }
        
        .highlight {
            color: #d4af37;
            font-weight: 600;
        }
        
        .cta-container {
            text-align: center;
            margin: 40px 0;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%);
            color: #0f0f0f;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 6px;
            font-family: 'Source Sans Pro', sans-serif;
            font-weight: 600;
            font-size: 18px;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            position: relative;
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.5);
        }
        
        .divider {
            height: 2px;
            background: linear-gradient(90deg, transparent 0%, rgba(212, 175, 55, 0.3) 20%, rgba(212, 175, 55, 0.8) 50%, rgba(212, 175, 55, 0.3) 80%, transparent 100%);
            margin: 30px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            left: 50%;
            top: -6px;
            transform: translateX(-50%) rotate(45deg);
            width: 12px;
            height: 12px;
            background: #d4af37;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
            background: rgba(212, 175, 55, 0.05);
            border: 1px solid rgba(212, 175, 55, 0.2);
            border-radius: 6px;
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 10px;
            display: block;
        }
        
        .feature-title {
            font-family: 'Poiret One', sans-serif;
            font-size: 18px;
            color: #d4af37;
            margin-bottom: 8px;
        }
        
        .feature-desc {
            font-size: 14px;
            color: #f5f5dc;
            opacity: 0.8;
        }
        
        .footer {
            background: #0f0f0f;
            padding: 30px;
            text-align: center;
            border-top: 1px solid rgba(212, 175, 55, 0.2);
        }
        
        .footer-text {
            font-size: 14px;
            color: #f5f5dc;
            opacity: 0.7;
            margin-bottom: 10px;
        }
        
        .footer-link {
            color: #d4af37;
            text-decoration: none;
        }
        
        .footer-link:hover {
            text-decoration: underline;
        }
        
        /* Mobile responsive */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .logo {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 20px;
            }
            
            .message {
                font-size: 15px;
            }
            
            .cta-button {
                padding: 14px 30px;
                font-size: 16px;
            }
            
            .content, .header, .footer {
                padding: 30px 20px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script>
        function copyToClipboard(text) {
            // For modern browsers
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopyFeedback();
                }).catch(function(err) {
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // Fallback for older browsers
                fallbackCopyTextToClipboard(text);
            }
        }
        
        function fallbackCopyTextToClipboard(text) {
        }
        
        function showCopyFeedback() {
        }
    </script>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">HOLLYWOOD TABLE</div>
            <div class="tagline">Where Stories Come to Life</div>
        </div>
        
        <div class="content">
            <div class="greeting">You're Invited to the Table</div>
            
            <p class="message">
                You've been personally invited to join <span class="highlight">Hollywood Table</span>, 
                an exclusive community where you can engage in intimate conversations with legendary 
                screenwriters and storytellers who shaped the golden age of cinema.
            </p>
            
            <p class="message">
                Step into our virtual speakeasy and discover the secrets behind the greatest stories 
                ever told. From noir mysteries to romantic comedies, from epic adventures to heartfelt 
                dramas - the masters are waiting to share their wisdom.
            </p>
            
            <div class="divider"></div>
            
            <div class="features">
                <div class="feature">
                    <span class="feature-icon">🎭</span>
                    <div class="feature-title">Master Screenwriters</div>
                    <div class="feature-desc">Chat with AI personalities inspired by Hollywood legends</div>
                </div>
                <div class="feature">
                    <span class="feature-icon">✨</span>
                    <div class="feature-title">Story Secrets</div>
                    <div class="feature-desc">Learn the craft from those who perfected it</div>
                </div>
                <div class="feature">
                    <span class="feature-icon">🍸</span>
                    <div class="feature-title">Intimate Setting</div>
                    <div class="feature-desc">A sophisticated noir atmosphere for creative minds</div>
                </div>
            </div>
            
            <div class="cta-container">
                <a href="{{ .ConfirmationURL }}" class="cta-button">
                    Accept Your Invitation
                </a>
            </div>
            
            <div class="divider"></div>
            
            <div style="background: rgba(212, 175, 55, 0.1); border: 1px solid rgba(212, 175, 55, 0.3); border-radius: 6px; padding: 20px; margin: 30px 0;">
                <div style="font-family: 'Cinzel', serif; font-size: 18px; color: #d4af37; text-align: center; margin-bottom: 15px;">
                    Share the Experience
                </div>
                <p style="font-size: 14px; color: #f5f5dc; text-align: center; margin-bottom: 15px; opacity: 0.9;">
                    You have <span class="highlight">3 exclusive invite codes</span> to share with fellow storytellers:
                </p>
                <div style="background: #0f0f0f; border: 1px solid rgba(212, 175, 55, 0.4); border-radius: 4px; padding: 15px;">
                    {{range $index, $code := .Data.inviteCodes}}
                    <div style="display: flex; align-items: center; justify-content: center; margin: 8px 0; padding: 8px 12px; background: rgba(212, 175, 55, 0.05); border: 1px solid rgba(212, 175, 55, 0.2); border-radius: 4px;">
                        <span style="color: #d4af37; font-size: 16px; letter-spacing: 2px; text-transform: uppercase; font-family: 'Courier New', monospace;">{{$code}}</span>
                    </div>
                    {{end}}
                </div>
                <p style="font-size: 12px; color: #f5f5dc; text-align: center; margin-top: 10px; opacity: 0.7; font-style: italic;">
                    Each code grants one seat at the table
                </p>
            </div>
            
            <p class="message" style="margin-top: 30px; font-size: 14px; opacity: 0.8;">
                This exclusive invitation was sent to you personally. 
                Follow the link above to claim your seat at the table and begin your journey 
                into the heart of Hollywood storytelling.
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                Welcome to {{ .SiteURL }}
            </p>
            <p class="footer-text">
                Questions? Contact us at <a href="mailto:support@{{ .SiteURL }}" class="footer-link">support@{{ .SiteURL }}</a>
            </p>
        </div>
    </div>
</body>
</html>